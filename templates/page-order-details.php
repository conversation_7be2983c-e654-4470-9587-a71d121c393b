<?php
/**
 * Template Name: Order details
 */

use <PERSON>z\Admin\Settings;
use <PERSON>z\Controllers\Attachments;
use <PERSON>z\Controllers\Quotations;
use <PERSON>z\Controllers\Uploader;
use <PERSON>z\Logger\QuotationHistory;
use <PERSON>z\Utils;

if ( ! isset( $_GET['order_id'] ) || ! ( $order = wc_get_order( wc_sanitize_order_id( $_GET['order_id'] ) ) ) ) {
	wp_redirect( home_url() );
	exit();
}

$order_id              = wc_sanitize_order_id( $_GET['order_id'] );
$provider              = get_post_meta( $order_id, '_provider', true );
$currentUserID         = get_current_user_id();
$isCurrentUserProvider = ( $currentUserID == $provider );

$quotation_id     = get_post_meta( $order->get_id(), '_quotation_id', true );
$offer_id         = get_post_meta( $order->get_id(), '_offer_id', true );
$provider         = get_user_by( 'email', get_comment_author_email( $offer_id ) );
$order_status     = $order->get_status();
$shipment_type    = get_post_meta( $quotation_id, '_shipment_type', true );
$origin_port      = get_post_meta( $quotation_id, '_origin_port', true );
$destination_port = get_post_meta( $quotation_id, '_destination_port', true );

$uploader = new Uploader();
$uploader->request_files( $order_id );

if ( isset( $_GET['file_op'] ) && isset( $_GET['file_id'] ) ) {
	if ( 'delete' == $_GET['file_op'] ) {
		$uploader->delete_file( intval( wc_clean( $_GET['file_id'] ) ) );
	}

	if ( 'share' == $_GET['file_op'] ) {
		$uploader->share_file( $order_id, intval( wc_clean( $_GET['file_id'] ) ) );
	}
}

/**
 * Get Shipment status
 */
if ( isset( $_POST['shipment_data'] ) ) {
	if ( ! isset( $_POST['_scac'] ) || ! isset( $_POST['_tdId'] ) || ! isset( $_POST['_tdType'] )) {
		$trackingResult = false;
	} else {
		update_post_meta( $order_id, '_scac', wc_clean( $_POST['_scac'] ) );
		update_post_meta( $order_id, '_tdId', wc_clean( $_POST['_tdId'] ) );
		update_post_meta( $order_id, '_tdType', wc_clean( $_POST['_tdType'] ) );
	}
}

$tracking       = new WooMarinetraffic\Tracking();
$trackingResult = $tracking->get_shipment_status( $order_id );


/**
 * Update shipment data.
 */
if ( isset( $_POST['update_data'] ) ) {
	( new Quotations() )->update_shipment_data_by_customer( $_POST, $quotation_id );
} elseif( isset( $_POST['remind_later' ] ) ) {
	set_transient( '_shipment_reminder_' . $order_id, 'true', 60*60*24 );
}

get_header();
?>
	<!--begin::Post-->
	<div class="post d-flex flex-column-fluid" id="kt_post">
		<!--begin::Container-->
		<div id="kt_content_container" class="container-xxl">
			<!--begin::Order details page-->
			<div class="d-flex flex-column gap-7 gap-lg-10">
				<!--begin::Order summary-->
				<div class="d-flex flex-column flex-xl-row gap-7 gap-lg-10">
					<div class="card card-flush py-4 flex-row-fluid">
						<!--begin::Card header-->
						<div class="card-header">
							<div class="card-title">
								<h2>Order Details (#<?php echo $order_id; ?>)</h2>
								<div class="badge badge-light-<?php if ( $order_status == 'processing' || $order_status == 'completed' ) {
									echo 'success';
								} else {
									echo 'warning';
								} ?>">
									<?php echo $order_status; ?>
								</div>
							</div>
						</div>
						<!--end::Card header-->
						<!--begin::Card body-->
						<div class="card-body pt-0">
							<div class="table-responsive">
								<!--begin::Table-->
								<table class="table align-middle table-row-bordered mb-0 fs-6 gy-5 min-w-300px">
									<!--begin::Table body-->
									<tbody class="fw-bold text-gray-600">
									<tr>
										<td class="text-muted">
											<div class="d-flex align-items-center">
												<!--begin::Svg Icon | path: icons/duotune/files/fil002.svg-->
												<span class="svg-icon svg-icon-2 me-2">
													<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21"
													     viewBox="0 0 20 21" fill="none">
														<path opacity="0.3"
														      d="M19 3.40002C18.4 3.40002 18 3.80002 18 4.40002V8.40002H14V4.40002C14 3.80002 13.6 3.40002 13 3.40002C12.4 3.40002 12 3.80002 12 4.40002V8.40002H8V4.40002C8 3.80002 7.6 3.40002 7 3.40002C6.4 3.40002 6 3.80002 6 4.40002V8.40002H2V4.40002C2 3.80002 1.6 3.40002 1 3.40002C0.4 3.40002 0 3.80002 0 4.40002V19.4C0 20 0.4 20.4 1 20.4H19C19.6 20.4 20 20 20 19.4V4.40002C20 3.80002 19.6 3.40002 19 3.40002ZM18 10.4V13.4H14V10.4H18ZM12 10.4V13.4H8V10.4H12ZM12 15.4V18.4H8V15.4H12ZM6 10.4V13.4H2V10.4H6ZM2 15.4H6V18.4H2V15.4ZM14 18.4V15.4H18V18.4H14Z"
														      fill="currentColor"/>
														<path d="M19 0.400024H1C0.4 0.400024 0 0.800024 0 1.40002V4.40002C0 5.00002 0.4 5.40002 1 5.40002H19C19.6 5.40002 20 5.00002 20 4.40002V1.40002C20 0.800024 19.6 0.400024 19 0.400024Z"
														      fill="currentColor"/>
													</svg>
												</span>
												<!--end::Svg Icon-->
												Date Added
											</div>
										</td>
										<td class="fw-bolder text-end"><?php echo $order->get_date_created(); ?></td>
									</tr>
									<tr>
										<td class="text-muted">
											<div class="d-flex align-items-center">
												<!--begin::Svg Icon | path: icons/duotune/finance/fin008.svg-->
												<span class="svg-icon svg-icon-2 me-2">
													<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path opacity="0.3" d="M3 13H10C10.6 13 11 13.4 11 14V21C11 21.6 10.6 22 10 22H3C2.4 22 2 21.6 2 21V14C2 13.4 2.4 13 3 13Z" fill="currentColor"/>
														<path d="M7 16H6C5.4 16 5 15.6 5 15V13H8V15C8 15.6 7.6 16 7 16Z" fill="currentColor"/>
														<path opacity="0.3" d="M14 13H21C21.6 13 22 13.4 22 14V21C22 21.6 21.6 22 21 22H14C13.4 22 13 21.6 13 21V14C13 13.4 13.4 13 14 13Z" fill="currentColor"/>
														<path d="M18 16H17C16.4 16 16 15.6 16 15V13H19V15C19 15.6 18.6 16 18 16Z" fill="currentColor"/>
														<path opacity="0.3" d="M3 2H10C10.6 2 11 2.4 11 3V10C11 10.6 10.6 11 10 11H3C2.4 11 2 10.6 2 10V3C2 2.4 2.4 2 3 2Z" fill="currentColor"/>
														<path d="M7 5H6C5.4 5 5 4.6 5 4V2H8V4C8 4.6 7.6 5 7 5Z" fill="currentColor"/>
														<path opacity="0.3" d="M14 2H21C21.6 2 22 2.4 22 3V10C22 10.6 21.6 11 21 11H14C13.4 11 13 10.6 13 10V3C13 2.4 13.4 2 14 2Z" fill="currentColor"/>
														<path d="M18 5H17C16.4 5 16 4.6 16 4V2H19V4C19 4.6 18.6 5 18 5Z" fill="currentColor"/>
													</svg>
												</span>
												<!--end::Svg Icon-->
												Shipment type
											</div>
										</td>
										<td class="fw-bolder text-end">
											<?php echo $shipment_type; ?>
										</td>
									</tr>
									<tr>
										<td class="text-muted">
											<div class="d-flex align-items-center">
												<!--begin::Svg Icon | path: icons/duotune/finance/fin008.svg-->
												<span class="svg-icon svg-icon-2 me-2">
													<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path d="M21 10H13V11C13 11.6 12.6 12 12 12C11.4 12 11 11.6 11 11V10H3C2.4 10 2 10.4 2 11V13H22V11C22 10.4 21.6 10 21 10Z" fill="currentColor"/>
														<path opacity="0.3" d="M12 12C11.4 12 11 11.6 11 11V3C11 2.4 11.4 2 12 2C12.6 2 13 2.4 13 3V11C13 11.6 12.6 12 12 12Z" fill="currentColor"/>
														<path opacity="0.3" d="M18.1 21H5.9C5.4 21 4.9 20.6 4.8 20.1L3 13H21L19.2 20.1C19.1 20.6 18.6 21 18.1 21ZM13 18V15C13 14.4 12.6 14 12 14C11.4 14 11 14.4 11 15V18C11 18.6 11.4 19 12 19C12.6 19 13 18.6 13 18ZM17 18V15C17 14.4 16.6 14 16 14C15.4 14 15 14.4 15 15V18C15 18.6 15.4 19 16 19C16.6 19 17 18.6 17 18ZM9 18V15C9 14.4 8.6 14 8 14C7.4 14 7 14.4 7 15V18C7 18.6 7.4 19 8 19C8.6 19 9 18.6 9 18Z" fill="currentColor"/>
													</svg>
												</span>
												<!--end::Svg Icon-->
												Load type
											</div>
										</td>
										<td class="fw-bolder text-end">
											<?php echo get_post_meta( $quotation_id, '_load_type', true ); ?>
										</td>
									</tr>
									<tr>
										<td class="text-muted">
											<div class="d-flex align-items-center">
												<!--begin::Svg Icon | path: icons/duotune/finance/fin008.svg-->
												<span class="svg-icon svg-icon-2 me-2">
													<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
													     viewBox="0 0 24 24" fill="none">
														<path opacity="0.3"
														      d="M3.20001 5.91897L16.9 3.01895C17.4 2.91895 18 3.219 18.1 3.819L19.2 9.01895L3.20001 5.91897Z"
														      fill="currentColor"/>
														<path opacity="0.3"
														      d="M13 13.9189C13 12.2189 14.3 10.9189 16 10.9189H21C21.6 10.9189 22 11.3189 22 11.9189V15.9189C22 16.5189 21.6 16.9189 21 16.9189H16C14.3 16.9189 13 15.6189 13 13.9189ZM16 12.4189C15.2 12.4189 14.5 13.1189 14.5 13.9189C14.5 14.7189 15.2 15.4189 16 15.4189C16.8 15.4189 17.5 14.7189 17.5 13.9189C17.5 13.1189 16.8 12.4189 16 12.4189Z"
														      fill="currentColor"/>
														<path d="M13 13.9189C13 12.2189 14.3 10.9189 16 10.9189H21V7.91895C21 6.81895 20.1 5.91895 19 5.91895H3C2.4 5.91895 2 6.31895 2 6.91895V20.9189C2 21.5189 2.4 21.9189 3 21.9189H19C20.1 21.9189 21 21.0189 21 19.9189V16.9189H16C14.3 16.9189 13 15.6189 13 13.9189Z"
														      fill="currentColor"/>
													</svg>
												</span>
												<!--end::Svg Icon-->
												Total
											</div>
										</td>
										<td class="fw-bolder text-end">
											<?php echo get_post_meta( $order->get_id(), '_original_price', true ) . ' ' . get_post_meta( $order->get_id(), '_original_currency', true ); ?>
										</td>
									</tr>
									<tr>
										<td class="text-muted">
											<div class="d-flex align-items-center">
												<!--begin::Svg Icon | path: icons/duotune/finance/fin008.svg-->
												<span class="svg-icon svg-icon-2 me-2">
													<svg width="24" height="24" viewBox="0 0 24 24" fill="none"
													     xmlns="http://www.w3.org/2000/svg">
														<path d="M8.7 4.19995L4 6.30005V18.8999L8.7 16.8V19L3.1 21.5C2.6 21.7 2 21.4 2 20.8V6C2 5.4 2.3 4.89995 2.9 4.69995L8.7 2.09998V4.19995Z"
														      fill="currentColor"/>
														<path d="M15.3 19.8L20 17.6999V5.09992L15.3 7.19989V4.99994L20.9 2.49994C21.4 2.29994 22 2.59989 22 3.19989V17.9999C22 18.5999 21.7 19.1 21.1 19.3L15.3 21.8998V19.8Z"
														      fill="currentColor"/>
														<path opacity="0.3"
														      d="M15.3 7.19995L20 5.09998V17.7L15.3 19.8V7.19995Z"
														      fill="currentColor"/>
														<path opacity="0.3"
														      d="M8.70001 4.19995V2L15.4 5V7.19995L8.70001 4.19995ZM8.70001 16.8V19L15.4 22V19.8L8.70001 16.8Z"
														      fill="currentColor"/>
														<path opacity="0.3"
														      d="M8.7 16.8L4 18.8999V6.30005L8.7 4.19995V16.8Z"
														      fill="currentColor"/>
													</svg>
												</span>
												<!--end::Svg Icon-->
												Origin Country
											</div>
										</td>
										<td class="fw-bolder text-end">
											<?php echo get_post_meta( $quotation_id, '_origin_country', true ); ?>
										</td>
									</tr>
									</tbody>
									<!--end::Table body-->
								</table>
								<!--end::Table-->
								<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#reminder_modal">
									Update Actual data
								</button>
							</div>
						</div>
						<!--end::Card body-->
					</div>
					<div class="card card-flush py-4 flex-row-fluid">
						<!--begin::Card header-->
						<div class="card-header">
							<div class="card-title">
								<h2>Service Provider</h2>
							</div>
						</div>
						<!--end::Card header-->
						<!--begin::Card body-->
						<div class="card-body pt-0">
							<div class="table-responsive">
								<!--begin::Table-->
								<table class="table align-middle table-row-bordered mb-0 fs-6 gy-5 min-w-300px">
									<!--begin::Table body-->
									<tbody class="fw-bold text-gray-600">
									<!--begin::Customer name-->
									<tr>
										<td class="text-muted">
											<div class="d-flex align-items-center">
												<!--begin::Svg Icon | path: icons/duotune/communication/com006.svg-->
												<span class="svg-icon svg-icon-2 me-2">
													<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
													     viewBox="0 0 24 24" fill="none">
														<path opacity="0.3"
														      d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 7C10.3 7 9 8.3 9 10C9 11.7 10.3 13 12 13C13.7 13 15 11.7 15 10C15 8.3 13.7 7 12 7Z"
														      fill="currentColor"/>
														<path d="M12 22C14.6 22 17 21 18.7 19.4C17.9 16.9 15.2 15 12 15C8.8 15 6.09999 16.9 5.29999 19.4C6.99999 21 9.4 22 12 22Z"
														      fill="currentColor"/>
													</svg>
												</span>
												<!--end::Svg Icon-->
												Name
											</div>
										</td>
										<td class="fw-bolder text-end">
											<div class="d-flex align-items-center justify-content-end">
												<?php echo $provider->display_name; ?>
											</div>
										</td>
									</tr>
									<!--end::Customer name-->
									<!--begin::Customer email-->
									<tr>
										<td class="text-muted">
											<div class="d-flex align-items-center">
												<!--begin::Svg Icon | path: icons/duotune/communication/com011.svg-->
												<span class="svg-icon svg-icon-2 me-2">
													<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
													     viewBox="0 0 24 24" fill="none">
														<path opacity="0.3"
														      d="M21 19H3C2.4 19 2 18.6 2 18V6C2 5.4 2.4 5 3 5H21C21.6 5 22 5.4 22 6V18C22 18.6 21.6 19 21 19Z"
														      fill="currentColor"/>
														<path d="M21 5H2.99999C2.69999 5 2.49999 5.10005 2.29999 5.30005L11.2 13.3C11.7 13.7 12.4 13.7 12.8 13.3L21.7 5.30005C21.5 5.10005 21.3 5 21 5Z"
														      fill="currentColor"/>
													</svg>
												</span>
												<!--end::Svg Icon-->
												Email
											</div>
										</td>
										<td class="fw-bolder text-end">
											<?php echo $provider->user_email; ?>
										</td>
									</tr>
									<!--end::Payment method-->
									<!--begin::Date-->
									<tr>
										<td class="text-muted">
											<div class="d-flex align-items-center">
												<!--begin::Svg Icon | path: icons/duotune/electronics/elc003.svg-->
												<span class="svg-icon svg-icon-2 me-2">
													<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
													     viewBox="0 0 24 24" fill="none">
														<path d="M5 20H19V21C19 21.6 18.6 22 18 22H6C5.4 22 5 21.6 5 21V20ZM19 3C19 2.4 18.6 2 18 2H6C5.4 2 5 2.4 5 3V4H19V3Z"
														      fill="currentColor"/>
														<path opacity="0.3" d="M19 4H5V20H19V4Z" fill="currentColor"/>
													</svg>
												</span>
												<!--end::Svg Icon-->
												Phone
											</div>
										</td>
										<td class="fw-bolder text-end">
											<?php echo get_user_meta( $provider->ID, 'phone', true ); ?>
										</td>
									</tr>
									<!--end::Date-->
									</tbody>
									<!--end::Table body-->
								</table>
								<!--end::Table-->
							</div>
						</div>
						<!--end::Card body-->
					</div>
					<div class="card card-flush py-4 flex-row-fluid">
						<!--begin::Card header-->
						<div class="card-header">
							<div class="card-title">
								<h2>Additional services</h2>
							</div>
						</div>
						<!--end::Card header-->
						<!--begin::Card body-->
						<div class="card-body pt-0">
							<div class="table-responsive">
								<table class="table align-middle table-row-bordered mb-0 fs-6 gy-5 min-w-300px">
									<tbody class="fw-bold text-gray-600">
									<tr>
										<td class="text-muted">
											<div class="d-flex align-items-center">
												<!--begin::Svg Icon | path: icons/duotune/communication/com006.svg-->
												<span class="svg-icon svg-icon-2 me-2">
													<svg width="24" height="24" viewBox="0 0 24 24" fill="none"
													     xmlns="http://www.w3.org/2000/svg">
														<path d="M6 20C6 20.6 5.6 21 5 21C4.4 21 4 20.6 4 20H6ZM18 20C18 20.6 18.4 21 19 21C19.6 21 20 20.6 20 20H18Z"
														      fill="currentColor"/>
														<path opacity="0.3"
														      d="M21 20H3C2.4 20 2 19.6 2 19V3C2 2.4 2.4 2 3 2H21C21.6 2 22 2.4 22 3V19C22 19.6 21.6 20 21 20ZM12 10H10.7C10.5 9.7 10.3 9.50005 10 9.30005V8C10 7.4 9.6 7 9 7C8.4 7 8 7.4 8 8V9.30005C7.7 9.50005 7.5 9.7 7.3 10H6C5.4 10 5 10.4 5 11C5 11.6 5.4 12 6 12H7.3C7.5 12.3 7.7 12.5 8 12.7V14C8 14.6 8.4 15 9 15C9.6 15 10 14.6 10 14V12.7C10.3 12.5 10.5 12.3 10.7 12H12C12.6 12 13 11.6 13 11C13 10.4 12.6 10 12 10Z"
														      fill="currentColor"/>
														<path d="M18.5 11C18.5 10.2 17.8 9.5 17 9.5C16.2 9.5 15.5 10.2 15.5 11C15.5 11.4 15.7 11.8 16 12.1V13C16 13.6 16.4 14 17 14C17.6 14 18 13.6 18 13V12.1C18.3 11.8 18.5 11.4 18.5 11Z"
														      fill="currentColor"/>
													</svg>
												</span>
												Insurance
											</div>
										</td>
										<td class="fw-bolder text-end">
											<div class="d-flex align-items-center justify-content-end">
												<?php echo get_post_meta( $quotation_id, '_insurance', true ); ?>
											</div>
										</td>
									</tr>
									<tr>
										<td class="text-muted">
											<div class="d-flex align-items-center">
												<!--begin::Svg Icon | path: icons/duotune/communication/com011.svg-->
												<span class="svg-icon svg-icon-2 me-2">
													<svg width="24" height="24" viewBox="0 0 24 24" fill="none"
													     xmlns="http://www.w3.org/2000/svg">
														<path d="M6 20C6 20.6 5.6 21 5 21C4.4 21 4 20.6 4 20H6ZM18 20C18 20.6 18.4 21 19 21C19.6 21 20 20.6 20 20H18Z"
														      fill="currentColor"/>
														<path opacity="0.3"
														      d="M21 20H3C2.4 20 2 19.6 2 19V3C2 2.4 2.4 2 3 2H21C21.6 2 22 2.4 22 3V19C22 19.6 21.6 20 21 20ZM12 10H10.7C10.5 9.7 10.3 9.50005 10 9.30005V8C10 7.4 9.6 7 9 7C8.4 7 8 7.4 8 8V9.30005C7.7 9.50005 7.5 9.7 7.3 10H6C5.4 10 5 10.4 5 11C5 11.6 5.4 12 6 12H7.3C7.5 12.3 7.7 12.5 8 12.7V14C8 14.6 8.4 15 9 15C9.6 15 10 14.6 10 14V12.7C10.3 12.5 10.5 12.3 10.7 12H12C12.6 12 13 11.6 13 11C13 10.4 12.6 10 12 10Z"
														      fill="currentColor"/>
														<path d="M18.5 11C18.5 10.2 17.8 9.5 17 9.5C16.2 9.5 15.5 10.2 15.5 11C15.5 11.4 15.7 11.8 16 12.1V13C16 13.6 16.4 14 17 14C17.6 14 18 13.6 18 13V12.1C18.3 11.8 18.5 11.4 18.5 11Z"
														      fill="currentColor"/>
													</svg>
												</span>
												<!--end::Svg Icon-->
												Customs Clearance
											</div>
										</td>
										<td class="fw-bolder text-end">
											<?php echo get_post_meta( $quotation_id, '_customs-clearance', true ); ?>
										</td>
									</tr>
									</tbody>
								</table>

                                <h2>Requested Dates</h2>
                                <table class="table align-middle table-row-bordered mb-0 fs-6 gy-5 min-w-300px">
                                    <tbody class="fw-bold text-gray-600">
                                    <tr>
                                        <td class="text-muted">
                                            <div class="d-flex align-items-center">
                                                <!--begin::Svg Icon | path: icons/duotune/communication/com006.svg-->
                                                <span class="svg-icon svg-icon-2 me-2">
													<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21"
                                                         viewBox="0 0 20 21" fill="none">
														<path opacity="0.3"
                                                              d="M19 3.40002C18.4 3.40002 18 3.80002 18 4.40002V8.40002H14V4.40002C14 3.80002 13.6 3.40002 13 3.40002C12.4 3.40002 12 3.80002 12 4.40002V8.40002H8V4.40002C8 3.80002 7.6 3.40002 7 3.40002C6.4 3.40002 6 3.80002 6 4.40002V8.40002H2V4.40002C2 3.80002 1.6 3.40002 1 3.40002C0.4 3.40002 0 3.80002 0 4.40002V19.4C0 20 0.4 20.4 1 20.4H19C19.6 20.4 20 20 20 19.4V4.40002C20 3.80002 19.6 3.40002 19 3.40002ZM18 10.4V13.4H14V10.4H18ZM12 10.4V13.4H8V10.4H12ZM12 15.4V18.4H8V15.4H12ZM6 10.4V13.4H2V10.4H6ZM2 15.4H6V18.4H2V15.4ZM14 18.4V15.4H18V18.4H14Z"
                                                              fill="currentColor"/>
														<path d="M19 0.400024H1C0.4 0.400024 0 0.800024 0 1.40002V4.40002C0 5.00002 0.4 5.40002 1 5.40002H19C19.6 5.40002 20 5.00002 20 4.40002V1.40002C20 0.800024 19.6 0.400024 19 0.400024Z"
                                                              fill="currentColor"/>
													</svg>
												</span>
                                                Pickup date
                                            </div>
                                        </td>
                                        <td class="fw-bolder text-end">
                                            <div class="d-flex align-items-center justify-content-end">
				                                <?php echo get_post_meta( $quotation_id, '_pick_up_date', true ); ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">
                                            <div class="d-flex align-items-center">
                                                <!--begin::Svg Icon | path: icons/duotune/communication/com011.svg-->
                                                <span class="svg-icon svg-icon-2 me-2">
													<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21"
                                                         viewBox="0 0 20 21" fill="none">
														<path opacity="0.3"
                                                              d="M19 3.40002C18.4 3.40002 18 3.80002 18 4.40002V8.40002H14V4.40002C14 3.80002 13.6 3.40002 13 3.40002C12.4 3.40002 12 3.80002 12 4.40002V8.40002H8V4.40002C8 3.80002 7.6 3.40002 7 3.40002C6.4 3.40002 6 3.80002 6 4.40002V8.40002H2V4.40002C2 3.80002 1.6 3.40002 1 3.40002C0.4 3.40002 0 3.80002 0 4.40002V19.4C0 20 0.4 20.4 1 20.4H19C19.6 20.4 20 20 20 19.4V4.40002C20 3.80002 19.6 3.40002 19 3.40002ZM18 10.4V13.4H14V10.4H18ZM12 10.4V13.4H8V10.4H12ZM12 15.4V18.4H8V15.4H12ZM6 10.4V13.4H2V10.4H6ZM2 15.4H6V18.4H2V15.4ZM14 18.4V15.4H18V18.4H14Z"
                                                              fill="currentColor"/>
														<path d="M19 0.400024H1C0.4 0.400024 0 0.800024 0 1.40002V4.40002C0 5.00002 0.4 5.40002 1 5.40002H19C19.6 5.40002 20 5.00002 20 4.40002V1.40002C20 0.800024 19.6 0.400024 19 0.400024Z"
                                                              fill="currentColor"/>
													</svg>
												</span>
                                                <!--end::Svg Icon-->
                                                Deliver By Date
                                            </div>
                                        </td>
                                        <td class="fw-bolder text-end">
			                                <?php echo get_post_meta( $quotation_id, '_deliver_by_date', true ); ?>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
							</div>
						</div>
						<!--end::Card body-->
					</div>
				</div>
				<!--end::Order summary-->
				<div class="d-flex flex-wrap flex-stack gap-5 gap-lg-10">
					<!--begin:::Tabs-->
					<ul id="order_tabs" class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-bold mb-lg-n2 me-auto">
						<!--begin:::Tab item-->
						<li class="nav-item">
							<a class="nav-link text-active-primary pb-4 active" data-bs-toggle="tab" href="#kt_ecommerce_sales_order_summary">
								Order Summary
							</a>
						</li>
						<!--end:::Tab item-->
						<?php
						if ( ! $isCurrentUserProvider ) {
							?>
							<!--begin:::Tab item-->
							<li class="nav-item">
								<a class="nav-link text-active-primary pb-4" data-bs-toggle="tab" href="#kt_ecommerce_sales_order_history">
									Order History
								</a>
							</li>
							<!--end:::Tab item-->
							<?php
						}
						?>
						<!--begin:::Tab item-->
						<li class="nav-item">
							<a class="nav-link text-active-primary pb-4" data-bs-toggle="tab" href="#kt_ecommerce_sales_order_files">
								Files
							</a>
						</li>
						<!--end:::Tab item-->

						<li class="nav-item hidden" style="display: none;">
							<a class="nav-link text-active-primary pb-4" data-bs-toggle="tab" href="#shipment_tracking">
								Tracking
							</a>
						</li>

						<?php
						if ( Settings::get_setting( 'customers_chat_feature' ) ) {
							?>
							<!--begin:::Tab item-->
							<li class="nav-item">
								<a class="nav-link text-active-primary pb-4" data-bs-toggle="tab" href="#order_chat">
									Chat
								</a>
							</li>
							<!--end:::Tab item-->
							<?php
						}
						?>
					</ul>
					<!--end:::Tabs-->
				</div>
				<!--begin::Tab content-->
				<div class="tab-content">
					<!--begin::Tab pane-->
					<div class="tab-pane fade show active" id="kt_ecommerce_sales_order_summary" role="tab-panel">
						<!--begin::Orders-->
						<div class="d-flex flex-column gap-7 gap-lg-10">
							<div class="d-flex flex-column flex-xl-row gap-7 gap-lg-10">
								<!--begin::Payment address-->
								<div class="card card-flush py-4 flex-row-fluid overflow-hidden">
									<!--begin::Background-->
									<div class="position-absolute top-0 end-0 opacity-10 pe-none text-end">
										<img src="<?php echo get_template_directory_uri() . '/'; ?>assets/media/icons/duotune/ecommerce/ecm001.svg"
										     class="w-175px"/>
									</div>
									<!--end::Background-->
									<!--begin::Card header-->
									<div class="card-header">
										<div class="card-title">
											<h2>Pick up address</h2>
										</div>
									</div>
									<div class="card-body pt-0">
										<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path opacity="0.3" d="M20.0381 4V10C20.0381 10.6 19.6381 11 19.0381 11H17.0381C16.4381 11 16.0381 10.6 16.0381 10V4C16.0381 2.9 16.9381 2 18.0381 2C19.1381 2 20.0381 2.9 20.0381 4ZM9.73808 18.9C10.7381 18.5 11.2381 17.3 10.8381 16.3L5.83808 3.29999C5.43808 2.29999 4.23808 1.80001 3.23808 2.20001C2.23808 2.60001 1.73809 3.79999 2.13809 4.79999L7.13809 17.8C7.43809 18.6 8.23808 19.1 9.03808 19.1C9.23808 19 9.53808 19 9.73808 18.9ZM19.0381 18H17.0381V20H19.0381V18Z" fill="currentColor"/>
											<path d="M18.0381 6H4.03809C2.93809 6 2.03809 5.1 2.03809 4C2.03809 2.9 2.93809 2 4.03809 2H18.0381C19.1381 2 20.0381 2.9 20.0381 4C20.0381 5.1 19.1381 6 18.0381 6ZM4.03809 3C3.43809 3 3.03809 3.4 3.03809 4C3.03809 4.6 3.43809 5 4.03809 5C4.63809 5 5.03809 4.6 5.03809 4C5.03809 3.4 4.63809 3 4.03809 3ZM18.0381 3C17.4381 3 17.0381 3.4 17.0381 4C17.0381 4.6 17.4381 5 18.0381 5C18.6381 5 19.0381 4.6 19.0381 4C19.0381 3.4 18.6381 3 18.0381 3ZM12.0381 17V22H6.03809V17C6.03809 15.3 7.33809 14 9.03809 14C10.7381 14 12.0381 15.3 12.0381 17ZM9.03809 15.5C8.23809 15.5 7.53809 16.2 7.53809 17C7.53809 17.8 8.23809 18.5 9.03809 18.5C9.83809 18.5 10.5381 17.8 10.5381 17C10.5381 16.2 9.83809 15.5 9.03809 15.5ZM15.0381 15H17.0381V13H16.0381V8L14.0381 10V14C14.0381 14.6 14.4381 15 15.0381 15ZM19.0381 15H21.0381C21.6381 15 22.0381 14.6 22.0381 14V10L20.0381 8V13H19.0381V15ZM21.0381 20H15.0381V22H21.0381V20Z" fill="currentColor"/>
										</svg>
										<?php echo $origin_port . ' ' . $shipment_type . ' port';?>
									</div>
									<!--end::Card header-->
									<!--begin::Card body-->
									<div class="card-body pt-0">
										<?php
										$address = get_post_meta( $quotation_id, '_pickup_address', true );
										if( strpos( $address, 'MAP::' ) !== false ) {
											$map = str_replace('MAP::','', $address);
											$address = 'https://www.google.com/maps/search/'.$map;
										}
										if ( strpos( $address, "http://" ) !== false || strpos( $address, "https://" ) !== false ) {
											echo '<a href="'. $address .'" target="_blank"><i class="bi bi-pin-map fs-2x text-primary"></i></a>';
										} else {
											echo str_replace( ",", "<br>", $address );;
										}
										?>
									</div>
									<!--end::Card body-->
								</div>
								<!--end::Payment address-->
								<!--begin::Shipping address-->
								<div class="card card-flush py-4 flex-row-fluid overflow-hidden">
									<!--begin::Background-->
									<div class="position-absolute top-0 end-0 opacity-10 pe-none text-end">
										<img src="<?php echo get_template_directory_uri() . '/'; ?>assets/media/icons/duotune/ecommerce/ecm006.svg"
										     class="w-175px"/>
									</div>
									<!--end::Background-->
									<!--begin::Card header-->
									<div class="card-header">
										<div class="card-title">
											<h2>Destination Address</h2>
										</div>
									</div>
									<div class="card-body pt-0">
										<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path opacity="0.3" d="M20.0381 4V10C20.0381 10.6 19.6381 11 19.0381 11H17.0381C16.4381 11 16.0381 10.6 16.0381 10V4C16.0381 2.9 16.9381 2 18.0381 2C19.1381 2 20.0381 2.9 20.0381 4ZM9.73808 18.9C10.7381 18.5 11.2381 17.3 10.8381 16.3L5.83808 3.29999C5.43808 2.29999 4.23808 1.80001 3.23808 2.20001C2.23808 2.60001 1.73809 3.79999 2.13809 4.79999L7.13809 17.8C7.43809 18.6 8.23808 19.1 9.03808 19.1C9.23808 19 9.53808 19 9.73808 18.9ZM19.0381 18H17.0381V20H19.0381V18Z" fill="currentColor"/>
											<path d="M18.0381 6H4.03809C2.93809 6 2.03809 5.1 2.03809 4C2.03809 2.9 2.93809 2 4.03809 2H18.0381C19.1381 2 20.0381 2.9 20.0381 4C20.0381 5.1 19.1381 6 18.0381 6ZM4.03809 3C3.43809 3 3.03809 3.4 3.03809 4C3.03809 4.6 3.43809 5 4.03809 5C4.63809 5 5.03809 4.6 5.03809 4C5.03809 3.4 4.63809 3 4.03809 3ZM18.0381 3C17.4381 3 17.0381 3.4 17.0381 4C17.0381 4.6 17.4381 5 18.0381 5C18.6381 5 19.0381 4.6 19.0381 4C19.0381 3.4 18.6381 3 18.0381 3ZM12.0381 17V22H6.03809V17C6.03809 15.3 7.33809 14 9.03809 14C10.7381 14 12.0381 15.3 12.0381 17ZM9.03809 15.5C8.23809 15.5 7.53809 16.2 7.53809 17C7.53809 17.8 8.23809 18.5 9.03809 18.5C9.83809 18.5 10.5381 17.8 10.5381 17C10.5381 16.2 9.83809 15.5 9.03809 15.5ZM15.0381 15H17.0381V13H16.0381V8L14.0381 10V14C14.0381 14.6 14.4381 15 15.0381 15ZM19.0381 15H21.0381C21.6381 15 22.0381 14.6 22.0381 14V10L20.0381 8V13H19.0381V15ZM21.0381 20H15.0381V22H21.0381V20Z" fill="currentColor"/>
										</svg>
										<?php echo $destination_port . ' ' . $shipment_type . ' port';?>
									</div>
									<!--end::Card header-->
									<!--begin::Card body-->
									<div class="card-body pt-0">
										<?php
										$address = get_post_meta( $quotation_id, '_destination_location', true );
										if( strpos( $address, 'MAP::' ) !== false ) {
											$map = str_replace('MAP::','', $address);
											$address = 'https://www.google.com/maps/search/'.$map;
										}
										if ( strpos( $address, "http://" ) !== false || strpos( $address, "https://" ) !== false ) {
											echo '<a href="'. $address .'" target="_blank"><i class="bi bi-pin-map fs-2x text-primary"></i></a>';
										} else {
											echo str_replace( ",", "<br>", $address );;
										}
										?>
									</div>
									<!--end::Card body-->
								</div>
								<!--end::Shipping address-->
							</div>
							<!--begin::Product List-->
							<div class="card card-flush py-4 flex-row-fluid overflow-hidden">
								<!--begin::Card header-->
								<div class="card-header">
									<div class="card-title">
										<h2>Containers</h2>
									</div>
								</div>
								<!--end::Card header-->
								<!--begin::Card body-->
								<div class="card-body pt-0">
									<div class="table-responsive">
										<!--begin::Table-->
										<table class="table align-middle table-row-dashed fs-6 gy-5 mb-0">
											<!--begin::Table head-->
											<thead>
											<tr class="text-start text-gray-400 fw-bolder fs-7 text-uppercase gs-0">
												<th class="min-w-175px">Type</th>
												<th class="min-w-100px text-end">Qty</th>
												<th class="min-w-100px text-end">#Pallets</th>
											</tr>
											</thead>
											<tbody class="fw-bold text-gray-600">
											<?php
											$pallets       = get_post_meta( $quotation_id, '_pallets', true );
											$quantity      = get_post_meta( $quotation_id, '_quantity', true );

											if ( 'sea' === $shipment_type ) {
												$specification = get_post_meta( $quotation_id, '_container-specification', true );
												foreach ( $quantity as $key => $c_aty ) {
													if ( $specification[ $key ] == '' ) {
														continue;
													}

													?>
													<tr>
														<td><?php echo $specification[ $key ]; ?></td>
														<td class="text-end"><?php echo $c_aty; ?></td>
														<td class="text-end"><?php echo $pallets[ $key ] ?? '-';?></td>
													</tr>
													<?php
												}
											}

											if ( 'air' === $shipment_type ) {
												$air_weight       = get_post_meta( $quotation_id, '_air_weight', true );
												$air_weight_uom   = get_post_meta( $quotation_id, '_air_weight_uom', true );
												$air_dimensions_l = get_post_meta( $quotation_id, '_air_dimensions_l', true );
												$air_dimensions_w = get_post_meta( $quotation_id, '_air_dimensions_w', true );
												$air_dimensions_h = get_post_meta( $quotation_id, '_air_dimensions_h', true );

												foreach ( $pallets as $key => $pallets_count ) {
													if ( $pallets_count == '' ) {
														continue;
													}

													?>
													<tr>
														<td><?php echo $air_weight[ $key ] . ' ' . $air_weight_uom[ $key ] . ' // ' . '( ' . $air_dimensions_l[ $key ] . 'x' . $air_dimensions_w[ $key ] . 'x' . $air_dimensions_h[ $key ] . ')'; ?></td>
														<td class="text-end"><?php echo $quantity[ $key ] ?? '1'; ?></td>
														<td class="text-end"><?php echo $pallets_count;?></td>
													</tr>
													<?php
												}
											}

											?>
											</tbody>
										</table>
										<!--end::Table-->
									</div>
								</div>
								<!--end::Card body-->
							</div>
							<!--end::Product List-->
						</div>
						<!--end::Orders-->
					</div>
					<!--end::Tab pane-->
					<?php
					if ( ! $isCurrentUserProvider ) {
						?>
						<!--begin::Tab pane-->
						<div class="tab-pane fade" id="kt_ecommerce_sales_order_history" role="tab-panel">
							<div class="d-flex flex-column flex-xl-row gap-7 gap-lg-10">
								<div class="card card-flush py-4 flex-row-fluid">
									<!--begin::Card header-->
									<div class="card-header">
										<div class="card-title">
											<h2>Tracking</h2>
										</div>
									</div>
									<!--end::Card header-->
									<!--begin::Card body-->
									<div class="card-body pt-0">
										<div class="stepper stepper-pills stepper-column d-flex flex-column flex-lg-row bg-white">
											<div class="d-flex flex-row-auto w-100 w-lg-300px">
												<div class="stepper-nav">
													<?php
													$notes            = $order->get_customer_order_notes();
													$_shipment_status = get_post_meta( $order->get_id(), '_shipment_status', true );
													?>
													<!--begin::Step 1-->
													<div class="stepper-item me-5 current" data-kt-stepper-element="nav">
														<!--begin::Line-->
														<div class="stepper-line w-40px">
															<!--begin::Stepper Title-->
															<!--end::Stepper Title-->
														</div>
														<!--end::Line-->

														<!--begin::Icon-->
														<div class="stepper-icon w-40px h-40px">
                                                        <span class="stepper-number"><i
		                                                        class="icon-xl la la-map text-white fs-2x text-center p-4"></i></span>
														</div>
														<!--end::Icon-->

														<!--begin::Label-->
														<div class="stepper-label">
															<h3 class="stepper-title text-black">
																<?php _e( 'Origin warehuse', 'wayz' ); ?>
															</h3>
															<div class="stepper-desc">
																-
															</div>
														</div>
														<!--end::Label-->
													</div>
													<!--end::Step 1-->

													<!--begin::Latest Step-->
													<div class="stepper-item me-5 <?php if ( $_shipment_status == 'to-origin-port' ) {
														echo 'current';
													} ?>" data-kt-stepper-element="nav">
														<!--begin::Line-->
														<div class="stepper-line w-40px"></div>
														<!--end::Line-->

														<!--begin::Icon-->
														<div class="stepper-icon w-40px h-40px">
															<i class="stepper-check fas fa-check"></i>
															<span class="stepper-number"><i
																	class="icon-xl la la-truck text-primary fs-2x text-center p-4"></i></span>
														</div>
														<!--begin::Icon-->

														<!--begin::Label-->
														<div class="stepper-label">
															<h3 class="stepper-title <?php if ( $_shipment_status == 'to-origin-port' ) {
																echo 'text-black';
															} ?>">
																<?php _e( 'On the way to origin port', 'wayz' ); ?>
															</h3>
															<div class="stepper-desc">
																<?php echo $origin_port; ?>
															</div>
														</div>
														<!--end::Label-->
													</div>
													<!--end::Latest Step-->

													<!--begin::Latest Step-->
													<div class="stepper-item me-5 <?php if ( $_shipment_status == 'at-origin-port' ) {
														echo 'current';
													} ?>" data-kt-stepper-element="nav">
														<!--begin::Line-->
														<div class="stepper-line w-40px"></div>
														<!--end::Line-->

														<!--begin::Icon-->
														<div class="stepper-icon w-40px h-40px">
															<i class="stepper-check fas fa-check"></i>
															<span class="stepper-number">
                                        <i class="icon-xl la la-map-pin text-primary fs-2x text-center p-4"></i>
                                    </span>
														</div>
														<!--begin::Icon-->

														<!--begin::Label-->
														<div class="stepper-label">
															<h3 class="stepper-title <?php if ( $_shipment_status == 'at-origin-port' ) {
																echo 'text-black';
															} ?>">
																<?php _e( 'Arrived at origin port', 'wayz' ); ?>
															</h3>
															<div class="stepper-desc">
																<?php echo $origin_port; ?>
															</div>
														</div>
														<!--end::Label-->
													</div>
													<!--end::Latest Step-->

													<!--begin::Latest Step-->
													<div class="stepper-item me-5 <?php if ( $_shipment_status == 'in-transit' ) {
														echo 'current';
													} ?>" data-kt-stepper-element="nav">
														<!--begin::Line-->
														<div class="stepper-line w-40px"></div>
														<!--end::Line-->

														<!--begin::Icon-->
														<div class="stepper-icon w-40px h-40px">
															<i class="stepper-check fas fa-check"></i>
															<span class="stepper-number">
                                        <i class="icon-xl la la-ship text-primary fs-2x text-center p-4"></i>
                                    </span>
														</div>
														<!--begin::Icon-->

														<!--begin::Label-->
														<div class="stepper-label">
															<h3 class="stepper-title <?php if ( $_shipment_status == 'in-transit' ) {
																echo 'text-black';
															} ?>">
																<?php _e( 'In transit', 'wayz' ); ?>
															</h3>
															<div class="stepper-desc">
																-
															</div>
														</div>
														<!--end::Label-->
													</div>
													<!--end::Latest Step-->

													<!--begin::Latest Step-->
													<div class="stepper-item me-5 <?php if ( $_shipment_status == 'at-destination-port' ) {
														echo 'current';
													} ?>" data-kt-stepper-element="nav">
														<!--begin::Line-->
														<div class="stepper-line w-40px"></div>
														<!--end::Line-->

														<!--begin::Icon-->
														<div class="stepper-icon w-40px h-40px">
															<i class="stepper-check fas fa-check"></i>
															<span class="stepper-number"><i
																	class="icon-xl la la-map-pin text-primary fs-2x text-center p-4"></i></span>
														</div>
														<!--begin::Icon-->

														<!--begin::Label-->
														<div class="stepper-label">
															<h3 class="stepper-title <?php if ( $order_status == 'completed' ) {
																echo 'text-black';
															} ?>">
																<?php _e( 'Arrived at destination port', 'wayz' ); ?>
															</h3>
															<div class="stepper-desc">
																<?php
																$address = get_post_meta( $quotation_id, '_destination_location', true );
																if( strpos( $address, 'MAP::' ) !== false ) {
																	$map = str_replace('MAP::','', $address);
																	$address = 'https://www.google.com/maps/search/'.$map;
																}
																if ( strpos( $address, "http://" ) !== false || strpos( $address, "https://" ) !== false ) {
																	echo '<a href="'. $address .'" target="_blank"><i class="bi bi-pin-map fs-2x text-primary"></i></a>';
																} else {
																	echo $address;
																}
																?>
															</div>
														</div>
														<!--end::Label-->
													</div>
													<!--end::Latest Step-->
												</div>
											</div>
										</div>
									</div>
									<!--end::Card body-->
								</div>
								<div class="card card-flush py-4 flex-row-fluid">
									<!--begin::Card header-->
									<div class="card-header">
										<div class="card-title">
											<h2>Notes</h2>
										</div>
									</div>
									<!--end::Card header-->
									<!--begin::Card body-->
									<div class="card-body pt-0">
										<div class="table-responsive">
											<!--begin::Table-->
											<table class="table align-middle table-row-dashed fs-6 gy-5 mb-0">
												<!--begin::Table head-->
												<thead>
												<tr class="text-start text-gray-400 fw-bolder fs-7 text-uppercase gs-0">
													<th class="min-w-100px">Date Added</th>
													<th class="min-w-175px">Comment</th>
												</tr>
												</thead>
												<!--end::Table head-->
												<!--begin::Table body-->
												<tbody class="fw-bold text-gray-600">
												<?php
												$notes = $order->get_customer_order_notes();
												foreach ( $notes as $note ) {
													?>
													<tr>
														<td>
															<?php echo $note->comment_date; ?>
														</td>
														<td>
															<?php echo $note->comment_content; ?>
														</td>
													</tr>
													<?php
												}
												?>
												</tbody>
												<!--end::Table head-->
											</table>
											<!--end::Table-->
										</div>
									</div>
									<!--end::Card body-->
								</div>
							</div>
							<div class="card card-flush mt-3">
								<!--begin::Card header-->
								<div class="card-header">
									<div class="card-title">
										<h2>History</h2>
									</div>
								</div>
								<!--end::Card header-->
								<!--begin::Card body-->
								<div class="card-body">
									<?php QuotationHistory::print_order_history( $order_id ); ?>
								</div>
								<!--end::Card body-->
							</div>
						</div>
						<!--end::Tab pane-->
						<?php
					}
					?>
					<!--begin::Tab pane-->
					<div class="tab-pane fade" id="kt_ecommerce_sales_order_files" role="tab-panel">
						<div class="d-flex flex-column flex-xl-row gap-7 gap-lg-10">
							<div class="card card-flush py-4 flex-row-fluid">
								<!--begin::Card header-->
								<div class="card-header pt-8">
									<div class="card-title">
										<!--begin::Search-->
										<div class="d-flex align-items-center position-relative my-1">
											<!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
											<span class="svg-icon svg-icon-1 position-absolute ms-6">
														<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
															<rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
															<path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor"></path>
														</svg>
													</span>
											<!--end::Svg Icon-->
											<input type="text" data-kt-filemanager-table-filter="search" class="form-control form-control-solid w-250px ps-15" placeholder="Search Files &amp; Folders">
										</div>
										<!--end::Search-->
									</div>
									<!--begin::Card toolbar-->
									<div class="card-toolbar">
										<!--begin::Toolbar-->
										<div class="d-flex justify-content-end" data-kt-filemanager-table-toolbar="base">
											<?php
											if ( ! $isCurrentUserProvider ) {
												?>
												<button type="button" data-bs-toggle="modal" data-bs-target="#request_file_modal" class="btn btn-light-primary me-3">
													<!--begin::Svg Icon | path: icons/duotune/files/fil013.svg-->
													<span class="svg-icon svg-icon-2">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path opacity="0.3" d="M10 4H21C21.6 4 22 4.4 22 5V7H10V4Z" fill="currentColor"></path>
                                                        <path d="M10.4 3.60001L12 6H21C21.6 6 22 6.4 22 7V19C22 19.6 21.6 20 21 20H3C2.4 20 2 19.6 2 19V4C2 3.4 2.4 3 3 3H9.2C9.7 3 10.2 3.20001 10.4 3.60001ZM16 12H13V9C13 8.4 12.6 8 12 8C11.4 8 11 8.4 11 9V12H8C7.4 12 7 12.4 7 13C7 13.6 7.4 14 8 14H11V17C11 17.6 11.4 18 12 18C12.6 18 13 17.6 13 17V14H16C16.6 14 17 13.6 17 13C17 12.4 16.6 12 16 12Z" fill="currentColor"></path>
                                                        <path opacity="0.3" d="M11 14H8C7.4 14 7 13.6 7 13C7 12.4 7.4 12 8 12H11V14ZM16 12H13V14H16C16.6 14 17 13.6 17 13C17 12.4 16.6 12 16 12Z" fill="currentColor"></path>
                                                    </svg>
                                                </span>
													<!--end::Svg Icon-->
													Request Files
												</button>
												<?php
											}
											?>
											<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#kt_modal_upload">
												<!--begin::Svg Icon | path: icons/duotune/files/fil018.svg-->
												<span class="svg-icon svg-icon-2">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path opacity="0.3" d="M10 4H21C21.6 4 22 4.4 22 5V7H10V4Z" fill="currentColor"></path>
                                                        <path d="M10.4 3.60001L12 6H21C21.6 6 22 6.4 22 7V19C22 19.6 21.6 20 21 20H3C2.4 20 2 19.6 2 19V4C2 3.4 2.4 3 3 3H9.20001C9.70001 3 10.2 3.20001 10.4 3.60001ZM16 11.6L12.7 8.29999C12.3 7.89999 11.7 7.89999 11.3 8.29999L8 11.6H11V17C11 17.6 11.4 18 12 18C12.6 18 13 17.6 13 17V11.6H16Z" fill="currentColor"></path>
                                                        <path opacity="0.3" d="M11 11.6V17C11 17.6 11.4 18 12 18C12.6 18 13 17.6 13 17V11.6H11Z" fill="currentColor"></path>
                                                    </svg>
                                                </span>
												<!--end::Svg Icon-->
												Upload Files
											</button>
										</div>
										<!--end::Toolbar-->
									</div>
									<!--end::Card toolbar-->
								</div>
								<!--end::Card header-->
								<!--begin::Card body-->
								<div class="card-body">
									<?php
									$attachments = new Attachments( $order_id );
									?>
									<!--begin::Table header-->
									<div class="d-flex flex-stack">
										<!--begin::Folder Stats-->
										<div class="badge badge-lg badge-primary">
											<span id="kt_file_manager_items_counter"><?php echo count( $attachments->atachments ); ?> files</span>
										</div>
										<!--end::Folder Stats-->
									</div>
									<!--end::Table header-->
									<!--begin::Table-->
									<div id="kt_file_manager_list_wrapper" class="dataTables_wrapper dt-bootstrap4 no-footer">
										<div class="table-responsive">
											<table id="kt_file_manager_list" data-kt-filemanager-table="blank" class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
												<!--begin::Table head-->
												<thead>
												<!--begin::Table row-->
												<tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
													<th class="min-w-250px sorting_disabled" rowspan="1" colspan="1" style="width: 554.25px;">Name</th>
													<th class="min-w-10px sorting_disabled" rowspan="1" colspan="1" style="width: 80.45px;">Size</th>
													<th class="min-w-125px" rowspan="1" colspan="1" style="width: 288.15px;">Date Added</th>
													<th class="min-w-125px sorting_disabled" rowspan="1" colspan="1" style="width: 288.15px;">Actions</th>
												</tr>
												<!--end::Table row-->
												</thead>
												<!--end::Table head-->
												<!--begin::Table body-->
												<tbody class="fw-semibold text-gray-600">
												<?php
												if ( empty( $attachments->atachments ) ) {
													?>
													<tr>
														<td colspan="5" class="dataTables_empty" valign="top">
															<div class="d-flex flex-column flex-center">
																<img src="<?php echo get_template_directory_uri() . '/'; ?>assets/media/illustrations/sketchy-1/5.png" class="mw-400px">
																<div class="fs-1 fw-bolder text-dark mb-4">No items found.</div>
																<div class="fs-6">Start requesting or uploading a new file!</div>
															</div>
														</td>
													</tr>
													<?php
												} else {
													foreach ( $attachments->atachments as $file ) {
														?>
														<tr>
															<!--begin::Name=-->
															<td>
																<div class="d-flex align-items-center">
																	<!--begin::Svg Icon | path: icons/duotune/files/fil003.svg-->
																	<span class="svg-icon svg-icon-2x svg-icon-primary me-4">
																	<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
																		<path opacity="0.3" d="M19 22H5C4.4 22 4 21.6 4 21V3C4 2.4 4.4 2 5 2H14L20 8V21C20 21.6 19.6 22 19 22Z" fill="currentColor"></path>
																		<path d="M15 8H20L14 2V7C14 7.6 14.4 8 15 8Z" fill="currentColor"></path>
																	</svg>
																</span>
																	<!--end::Svg Icon-->
																	<a href="<?php echo $file['download_url']; ?>" target="_blank" class="text-gray-800 text-hover-primary"><?php echo $file['name']; ?></a>
																</div>
															</td>
															<!--end::Name=-->
															<!--begin::Size-->
															<td><?php echo $file['size']; ?></td>
															<!--end::Size-->
															<!--begin::Last modified-->
															<td data-order="<?php echo $file['date']; ?>"><?php echo $file['date']; ?></td>
															<!--end::Last modified-->
															<td>
																<?php
																if ( 6300 > ( current_time( 'timestamp' ) - strtotime( $file['date'] ) ) ) {
																	$delete_link = add_query_arg( [
																		'file_op' => 'delete',
																		'file_id' => $file['id']
																	] );
																	?>
																	<a href="<?php echo $delete_link; ?>" class="btn btn-danger fs-8">Delete</a>
																	<?php
																}

																if ( $order->get_customer_id() === $currentUserID ) {
																	$share_link = add_query_arg( [
																		'file_op' => 'share',
																		'file_id' => $file['id']
																	] );
																	?>
																	<a href="<?php echo $share_link; ?>" class="btn btn-info fs-8">Share</a>
																	<?php
																}
																?>
															</td>
														</tr>
														<?php
													}
												}
												?>
												</tbody>
												<!--end::Table body-->
											</table>
										</div>
									</div>
									<!--end::Table-->
								</div>
								<!--end::Card body-->
							</div>
						</div>
					</div>
					<!--end::Tab pane-->
					<!--begin::Tab pane-->
					<div class="tab-pane fade" id="shipment_tracking">
						<div class="row gy-5 g-xl-10">
							<div class="col-xl-8 mb-5 mb-xl-10">
								<div class="card card-flush">
									<!--begin::Body-->
									<div class="card-body">
										<ul class="nav nav-pills nav-pills-custom row position-relative mx-0 mb-9" role="tablist">
											<!--begin::Item-->
											<li class="nav-item col-4 mx-0 p-0" role="presentation">
												<!--begin::Link-->
												<a class="nav-link active d-flex justify-content-center w-100 border-0 h-100" data-bs-toggle="pill" href="#transportation_plan" aria-selected="true" role="tab">
													<!--begin::Subtitle-->
													<span class="nav-text text-gray-800 fw-bold fs-6 mb-3">TRANSPORTATION PLAN</span>
													<!--end::Subtitle-->
													<!--begin::Bullet-->
													<span class="bullet-custom position-absolute z-index-2 bottom-0 w-100 h-4px bg-primary rounded"></span>
													<!--end::Bullet-->
												</a>
												<!--end::Link-->
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="nav-item col-4 mx-0 p-0" role="presentation">
												<!--begin::Link-->
												<a class="nav-link d-flex justify-content-center w-100 border-0 h-100" data-bs-toggle="pill" href="#kt_list_widget_10_tab_1" aria-selected="true" role="tab">
													<!--begin::Subtitle-->
													<span class="nav-text text-gray-800 fw-bold fs-6 mb-3">CARRIER EVENTS</span>
													<!--end::Subtitle-->
													<!--begin::Bullet-->
													<span class="bullet-custom position-absolute z-index-2 bottom-0 w-100 h-4px bg-primary rounded"></span>
													<!--end::Bullet-->
												</a>
												<!--end::Link-->
											</li>
											<!--end::Item-->
											<!--begin::Bullet-->
											<span class="position-absolute z-index-1 bottom-0 w-100 h-4px bg-light rounded"></span>
											<!--end::Bullet-->
										</ul>
										<!--begin::Tab Content-->
										<div class="tab-content">
											<div class="tab-pane fade active show" id="transportation_plan" role="tabpanel">
												<?php
												$_marinetrafficData  = get_post_meta( $order_id, '_marinetrafficData', true );
												$_transportationPlan = $_marinetrafficData['transportationPlan'] ?? [];
												if ( ! empty( $_transportationPlan ) ) {
													?>
													<!--begin::Item-->
													<div class="m-0">
														<!--begin::Wrapper-->
														<div class="d-flex align-items-sm-center mb-5">
															<!--begin::Symbol-->
															<div class="symbol symbol-45px me-4">
															<span class="symbol-label bg-<?php if ( ! is_null( $_marinetrafficData['originPortActualDepartureUtc'] ) ) {
																echo 'primary';
															} else {
																echo 'light';
															} ?>">
																<i class="text-inverse-<?php if ( ! is_null( $_marinetrafficData['originPortActualDepartureUtc'] ) ) {
																	echo 'primary';
																} else {
																	echo 'light';
																} ?> fs-1 lh-0 fonticon-ship"></i>
															</span>
															</div>
															<!--end::Symbol-->
															<!--begin::Section-->
															<div class="d-flex align-items-center flex-row-fluid flex-wrap">
																<div class="flex-grow-1 me-2">
																	<a href="#" class="text-gray-400 fs-6 fw-semibold">
																	<span class="svg-icon svg-icon-muted">
																		<svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
																			<path d="M17.5 7.89998L14.8 14.8L13.4 13.3999C14.2 12.5999 14.2 11.4001 13.4 10.6001C12.6 9.80006 11.4 9.80006 10.6 10.6001L9.20001 9.19991L16.1 6.49996C17 6.19996 17.8 6.99998 17.5 7.89998ZM12.7 11.3C12.3 10.9 11.7 10.9 11.3 11.3C10.9 11.7 10.9 12.2999 11.3 12.6999C11.7 13.0999 12.3 13.0999 12.7 12.6999C13.1 12.2999 13.1 11.7 12.7 11.3Z" fill="currentColor"/>
																			<path opacity="0.3" d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM16.1 6.5L9.20001 9.19995L6.5 16.1001C6.2 17.0001 6.99999 17.8 7.89999 17.5L14.8 14.8L17.5 7.90002C17.8 7.00002 17 6.2 16.1 6.5Z" fill="currentColor"/>
																		</svg>
																	</span>
																		On <?php echo $_marinetrafficData['vesselName']; ?>
																	</a>
																	<span class="text-gray-800 fw-bold d-block fs-4"><?php echo $_marinetrafficData['originPortName'] . ', ' . $_marinetrafficData['originPortCountry']; ?></span>
																</div>
															</div>
															<!--end::Section-->
														</div>
														<!--end::Wrapper-->
														<!--begin::Timeline-->
														<div class="timeline">
															<!--begin::Timeline item-->
															<div class="timeline-item align-items-center mb-7">
																<!--begin::Timeline icon-->
																<div class="timeline-icon" style="margin-left: 11px">
																	<!--begin::Svg Icon | path: icons/duotune/general/gen018.svg-->
																	<span class="svg-icon svg-icon-2 svg-icon-<?php if ( ! is_null( $_marinetrafficData['originPortPredictiveDepartureUtc'] ) ) {
																		echo 'info';
																	} else {
																		echo 'muted';
																	} ?>">
																	<!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/kt-products/docs/metronic/html/releases/2022-11-24-050857/core/html/src/media/icons/duotune/maps/map006.svg-->
																	<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
																		<path opacity="0.3" d="M20.9 12.9C20.3 12.9 19.9 12.5 19.9 11.9C19.9 11.3 20.3 10.9 20.9 10.9H21.8C21.3 6.2 17.6 2.4 12.9 2V2.9C12.9 3.5 12.5 3.9 11.9 3.9C11.3 3.9 10.9 3.5 10.9 2.9V2C6.19999 2.5 2.4 6.2 2 10.9H2.89999C3.49999 10.9 3.89999 11.3 3.89999 11.9C3.89999 12.5 3.49999 12.9 2.89999 12.9H2C2.5 17.6 6.19999 21.4 10.9 21.8V20.9C10.9 20.3 11.3 19.9 11.9 19.9C12.5 19.9 12.9 20.3 12.9 20.9V21.8C17.6 21.3 21.4 17.6 21.8 12.9H20.9Z" fill="currentColor"/>
																		<path d="M16.9 10.9H13.6C13.4 10.6 13.2 10.4 12.9 10.2V5.90002C12.9 5.30002 12.5 4.90002 11.9 4.90002C11.3 4.90002 10.9 5.30002 10.9 5.90002V10.2C10.6 10.4 10.4 10.6 10.2 10.9H9.89999C9.29999 10.9 8.89999 11.3 8.89999 11.9C8.89999 12.5 9.29999 12.9 9.89999 12.9H10.2C10.4 13.2 10.6 13.4 10.9 13.6V13.9C10.9 14.5 11.3 14.9 11.9 14.9C12.5 14.9 12.9 14.5 12.9 13.9V13.6C13.2 13.4 13.4 13.2 13.6 12.9H16.9C17.5 12.9 17.9 12.5 17.9 11.9C17.9 11.3 17.5 10.9 16.9 10.9Z" fill="currentColor"/>
																	</svg>
																		<!--end::Svg Icon-->
																</span>
																	<!--end::Svg Icon-->
																</div>
																<!--end::Timeline icon-->
																<!--begin::Timeline content-->
																<div class="timeline-content m-0">
																	<!--begin::Title-->
																	<span class="fs-6 fw-bold text-gray-700">Departure: </span>
																	<span class="fs-6 fw-bold text-gray-500">
																	<?php echo $_marinetrafficData['originPortActualDepartureUtc'] ?? $_marinetrafficData['portPredictiveDepartureUtc'] . " ( Predictive )"; ?>
																</span>
																	<!--end::Title-->
																</div>
																<!--end::Timeline content-->
															</div>
															<!--end::Timeline item-->
														</div>
														<!--end::Timeline-->
													</div>
													<!--end::Item-->
													<!--begin::Separator-->
													<div class="separator separator-dashed my-6"></div>
													<!--end::Separator-->
													<?php
													if ( isset( $_transportationPlan['transportLegs'] ) && is_array( $_transportationPlan['transportLegs'] ) ) {
														foreach ( $_transportationPlan['transportLegs'] as $indexKey => $transportLeg ) {
															?>
															<!--begin::Item-->
															<div class="m-0">
																<!--begin::Wrapper-->
																<div class="d-flex align-items-sm-center mb-5">
																	<!--begin::Symbol-->
																	<div class="symbol symbol-45px me-4">
																	<span class="symbol-label bg-<?php if ( ! is_null( $transportLeg['portActualArrivalUtc'] ) ) {
																		echo 'primary';
																	} else {
																		echo 'light';
																	} ?>">
																		<i class="text-inverse-<?php if ( ! is_null( $transportLeg['portActualArrivalUtc'] ) ) {
																			echo 'primary';
																		} else {
																			echo 'light';
																		} ?> fs-1 lh-0 fonticon-ship"></i>
																	</span>
																	</div>
																	<!--end::Symbol-->
																	<!--begin::Section-->
																	<div class="d-flex align-items-center flex-row-fluid flex-wrap">
																		<div class="flex-grow-1 me-2">
																			<?php
																			$portKey    = array_search( $transportLeg['arrivalLocation']['portId'], array_column( $_transportationPlan['locations'], 'portId' ) );
																			$portName   = $_transportationPlan['locations'][ $portKey ]['locationName'] . ', ' . $_transportationPlan['locations'][ $portKey ]['locationCountry'];
																			$vesselKey  = array_search( $transportLeg['vessel']['shipId'], array_column( $_transportationPlan['vessels'], 'shipId' ) );
																			$vesselName = $_transportationPlan['vessels'][ $vesselKey ]['vesselName'];
																			?>
																			<a href="#" class="text-gray-400 fs-6 fw-semibold">
																			<span class="svg-icon svg-icon-muted">
																				<svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
																					<path d="M17.5 7.89998L14.8 14.8L13.4 13.3999C14.2 12.5999 14.2 11.4001 13.4 10.6001C12.6 9.80006 11.4 9.80006 10.6 10.6001L9.20001 9.19991L16.1 6.49996C17 6.19996 17.8 6.99998 17.5 7.89998ZM12.7 11.3C12.3 10.9 11.7 10.9 11.3 11.3C10.9 11.7 10.9 12.2999 11.3 12.6999C11.7 13.0999 12.3 13.0999 12.7 12.6999C13.1 12.2999 13.1 11.7 12.7 11.3Z" fill="currentColor"/>
																					<path opacity="0.3" d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM16.1 6.5L9.20001 9.19995L6.5 16.1001C6.2 17.0001 6.99999 17.8 7.89999 17.5L14.8 14.8L17.5 7.90002C17.8 7.00002 17 6.2 16.1 6.5Z" fill="currentColor"/>
																				</svg>
																			</span>
																				On <?php echo $vesselName; ?>
																			</a>
																			<span class="text-gray-800 fw-bold d-block fs-4"><?php echo $portName; ?></span>
																		</div>
																	</div>
																	<!--end::Section-->
																</div>
																<!--end::Wrapper-->
																<!--begin::Timeline-->
																<div class="timeline">
																	<!--begin::Timeline item-->
																	<div class="timeline-item align-items-center mb-7">
																		<!--begin::Timeline line-->
																		<div class="timeline-line w-40px mt-6 mb-n12"></div>
																		<!--end::Timeline line-->
																		<!--begin::Timeline icon-->
																		<div class="timeline-icon" style="margin-left: 11px">
																			<!--begin::Svg Icon | path: icons/duotune/general/gen018.svg-->
																			<span class="svg-icon svg-icon-2 svg-icon-<?php if ( ! is_null( $transportLeg['portActualArrivalUtc'] ) ) {
																				echo 'info';
																			} else {
																				echo 'muted';
																			} ?>">
																			<!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/kt-products/docs/metronic/html/releases/2022-11-24-050857/core/html/src/media/icons/duotune/maps/map006.svg-->
																			<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
																				<path opacity="0.3" d="M20.9 12.9C20.3 12.9 19.9 12.5 19.9 11.9C19.9 11.3 20.3 10.9 20.9 10.9H21.8C21.3 6.2 17.6 2.4 12.9 2V2.9C12.9 3.5 12.5 3.9 11.9 3.9C11.3 3.9 10.9 3.5 10.9 2.9V2C6.19999 2.5 2.4 6.2 2 10.9H2.89999C3.49999 10.9 3.89999 11.3 3.89999 11.9C3.89999 12.5 3.49999 12.9 2.89999 12.9H2C2.5 17.6 6.19999 21.4 10.9 21.8V20.9C10.9 20.3 11.3 19.9 11.9 19.9C12.5 19.9 12.9 20.3 12.9 20.9V21.8C17.6 21.3 21.4 17.6 21.8 12.9H20.9Z" fill="currentColor"/>
																				<path d="M16.9 10.9H13.6C13.4 10.6 13.2 10.4 12.9 10.2V5.90002C12.9 5.30002 12.5 4.90002 11.9 4.90002C11.3 4.90002 10.9 5.30002 10.9 5.90002V10.2C10.6 10.4 10.4 10.6 10.2 10.9H9.89999C9.29999 10.9 8.89999 11.3 8.89999 11.9C8.89999 12.5 9.29999 12.9 9.89999 12.9H10.2C10.4 13.2 10.6 13.4 10.9 13.6V13.9C10.9 14.5 11.3 14.9 11.9 14.9C12.5 14.9 12.9 14.5 12.9 13.9V13.6C13.2 13.4 13.4 13.2 13.6 12.9H16.9C17.5 12.9 17.9 12.5 17.9 11.9C17.9 11.3 17.5 10.9 16.9 10.9Z" fill="currentColor"/>
																			</svg>
																				<!--end::Svg Icon-->
																		</span>
																			<!--end::Svg Icon-->
																		</div>
																		<!--end::Timeline icon-->
																		<!--begin::Timeline content-->
																		<div class="timeline-content m-0">
																			<!--begin::Title-->
																			<span class="fs-6 fw-bold text-gray-700">Arrival:  </span>
																			<span class="fs-6 fw-bold text-gray-500"><?php echo $transportLeg['portActualArrivalUtc'] ?? $transportLeg['portPredictiveArrivalUtc'] . " ( Predictive )"; ?></span>
																			<!--end::Title-->
																		</div>
																		<!--end::Timeline content-->
																	</div>
																	<!--end::Timeline item-->
																	<!--begin::Timeline item-->
																	<div class="timeline-item align-items-center">
																		<!--begin::Timeline icon-->
																		<div class="timeline-icon" style="margin-left: 11px">
																			<!--begin::Svg Icon | path: icons/duotune/general/gen018.svg-->
																			<span class="svg-icon svg-icon-2 svg-icon-<?php if ( ! is_null( $_transportationPlan['transportLegs'][ $indexKey + 1 ]['portActualDepartureUtc'] ) ) {
																				echo 'info';
																			} else {
																				echo 'muted';
																			} ?>">
																			<!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/kt-products/docs/metronic/html/releases/2022-11-24-050857/core/html/src/media/icons/duotune/maps/map006.svg-->
																			<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
																				<path opacity="0.3" d="M20.9 12.9C20.3 12.9 19.9 12.5 19.9 11.9C19.9 11.3 20.3 10.9 20.9 10.9H21.8C21.3 6.2 17.6 2.4 12.9 2V2.9C12.9 3.5 12.5 3.9 11.9 3.9C11.3 3.9 10.9 3.5 10.9 2.9V2C6.19999 2.5 2.4 6.2 2 10.9H2.89999C3.49999 10.9 3.89999 11.3 3.89999 11.9C3.89999 12.5 3.49999 12.9 2.89999 12.9H2C2.5 17.6 6.19999 21.4 10.9 21.8V20.9C10.9 20.3 11.3 19.9 11.9 19.9C12.5 19.9 12.9 20.3 12.9 20.9V21.8C17.6 21.3 21.4 17.6 21.8 12.9H20.9Z" fill="currentColor"/>
																				<path d="M16.9 10.9H13.6C13.4 10.6 13.2 10.4 12.9 10.2V5.90002C12.9 5.30002 12.5 4.90002 11.9 4.90002C11.3 4.90002 10.9 5.30002 10.9 5.90002V10.2C10.6 10.4 10.4 10.6 10.2 10.9H9.89999C9.29999 10.9 8.89999 11.3 8.89999 11.9C8.89999 12.5 9.29999 12.9 9.89999 12.9H10.2C10.4 13.2 10.6 13.4 10.9 13.6V13.9C10.9 14.5 11.3 14.9 11.9 14.9C12.5 14.9 12.9 14.5 12.9 13.9V13.6C13.2 13.4 13.4 13.2 13.6 12.9H16.9C17.5 12.9 17.9 12.5 17.9 11.9C17.9 11.3 17.5 10.9 16.9 10.9Z" fill="currentColor"/>
																			</svg>
																				<!--end::Svg Icon-->
																		</span>
																			<!--end::Svg Icon-->
																		</div>
																		<!--end::Timeline icon-->
																		<!--begin::Timeline content-->
																		<div class="timeline-content m-0">
																			<!--begin::Title-->
																			<span class="fs-6 fw-bold text-gray-700">Departure: </span>
																			<span class="fs-6 fw-bold text-gray-500">
																			<?php
																			if ( isset( $_transportationPlan['transportLegs'][ $indexKey + 1 ] ) ) {
																				echo $_transportationPlan['transportLegs'][ $indexKey + 1 ]['portActualDepartureUtc'] ?? $_transportationPlan['transportLegs'][ $indexKey + 1 ]['portPredictiveDepartureUtc'] . " ( Predictive )";
																			} else {
																				echo '-';
																			}
																			?>
																		</span>
																			<!--end::Title-->
																		</div>
																		<!--end::Timeline content-->
																	</div>
																	<!--end::Timeline item-->
																</div>
																<!--end::Timeline-->
															</div>
															<!--end::Item-->
															<!--begin::Separator-->
															<div class="separator separator-dashed my-6"></div>
															<!--end::Separator-->
															<?php
														}
													}
												}
												?>
											</div>
											<div class="tab-pane fade" id="kt_list_widget_10_tab_1" role="tabpanel">
												<?php
												if ( isset( $_transportationPlan['events'] ) && is_array( $_transportationPlan['events'] ) ) {
													foreach ( $_transportationPlan['events'] as $event ) {
														?>
														<!--begin::Item-->
														<div class="m-0">
															<!--begin::Wrapper-->
															<div class="d-flex align-items-sm-center mb-5">
																<!--begin::Symbol-->
																<div class="symbol symbol-45px me-4">
															<span class="symbol-label bg-<?php if ( 'ACT' === $event['eventStatus'] ) {
																echo 'primary';
															} else {
																echo 'light';
															} ?>">
																<i class="text-inverse-<?php if ( 'ACT' === $event['eventStatus'] ) {
																	echo 'primary';
																} else {
																	echo 'light';
																} ?> fs-1 lh-0 <?php if ( 'LAND' === $event['eventCategory'] ) {
																	echo 'fonticon-truck';
																} else {
																	echo 'fonticon-ship';
																} ?>"></i>
															</span>
																</div>
																<!--end::Symbol-->
																<!--begin::Section-->
																<div class="d-flex align-items-center flex-row-fluid flex-wrap">
																	<div class="flex-grow-1 me-2">
																		<a href="#" class="text-gray-400 fs-6 fw-semibold"><?php echo $event['eventDatetime']; ?></a>
																		<span class="text-gray-800 fw-bold d-block fs-4"><?php echo $event['eventTypeDescription']; ?></span>
																	</div>
																	<span class="badge badge-lg badge-light-<?php if ( 'ACT' === $event['eventStatus'] ) {
																		echo 'success';
																	} else {
																		echo 'warning';
																	} ?> fw-bold my-2"><?php echo $event['eventStatus']; ?></span>
																</div>
																<!--end::Section-->
															</div>
															<!--end::Wrapper-->
															<!--begin::Timeline-->
															<div class="timeline">
																<!--begin::Timeline item-->
																<div class="timeline-item align-items-center">
																	<!--begin::Timeline line-->
																	<div class="timeline-line w-40px"></div>
																	<!--end::Timeline line-->
																	<!--begin::Timeline icon-->
																	<div class="timeline-icon" style="margin-left: 11px">
																		<!--begin::Svg Icon | path: icons/duotune/general/gen018.svg-->
																		<span class="svg-icon svg-icon-2 svg-icon-info">
																	<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
																		<path opacity="0.3" d="M18.0624 15.3453L13.1624 20.7453C12.5624 21.4453 11.5624 21.4453 10.9624 20.7453L6.06242 15.3453C4.56242 13.6453 3.76242 11.4453 4.06242 8.94534C4.56242 5.34534 7.46242 2.44534 11.0624 2.04534C15.8624 1.54534 19.9624 5.24534 19.9624 9.94534C20.0624 12.0453 19.2624 13.9453 18.0624 15.3453Z" fill="currentColor"></path>
																		<path d="M12.0624 13.0453C13.7193 13.0453 15.0624 11.7022 15.0624 10.0453C15.0624 8.38849 13.7193 7.04535 12.0624 7.04535C10.4056 7.04535 9.06241 8.38849 9.06241 10.0453C9.06241 11.7022 10.4056 13.0453 12.0624 13.0453Z" fill="currentColor"></path>
																	</svg>
																</span>
																		<!--end::Svg Icon-->
																	</div>
																	<!--end::Timeline icon-->
																	<!--begin::Timeline content-->
																	<div class="timeline-content m-0">
																		<!--begin::Title-->
																		<?php
																		$portKey  = array_search( $event['location']['portId'], array_column( $_transportationPlan['locations'], 'portId' ) );
																		$portName = $_transportationPlan['locations'][ $portKey ]['locationName'] . ', ' . $_transportationPlan['locations'][ $portKey ]['locationCountry'];
																		?>
																		<span class="fs-6 fw-bold text-gray-800"><?php echo $portName; ?></span>
																		<!--end::Title-->
																	</div>
																	<!--end::Timeline content-->
																</div>
																<!--end::Timeline item-->
															</div>
															<!--end::Timeline-->
														</div>
														<!--end::Item-->
														<!--begin::Separator-->
														<div class="separator separator-dashed my-6"></div>
														<!--end::Separator-->
														<?php
													}
												}
												?>
											</div>
										</div>
										<!--end::Tab Content-->
									</div>
									<!--end: Card Body-->
								</div>
							</div>
							<div class="col-xl-4 mb-5 mb-xl-2">
								<div class="card card-flush">
									<!--begin::Body-->
									<div class="card-body">
										<?php
										$scac     = get_post_meta( $order_id, '_scac', true );
										$tdId     = get_post_meta( $order_id, '_tdId', true );
										$tdType   = get_post_meta( $order_id, '_tdType', true );
										?>
										<form action="#shipment_tracking" class="form mb-15 fv-plugins-bootstrap5 fv-plugins-framework" method="post">
											<h1 class="fw-bold text-dark mb-9">Shipment data :</h1>
											<!--begin::Input group-->
											<div class="d-flex flex-column mb-5 fv-row">
												<!--begin::Label-->
												<label class="fs-5 fw-semibold mb-2" for="_scac">SCAC Code</label>
												<!--end::Label-->
												<!--begin::Input-->
												<input required class="form-control form-control-solid" name="_scac" id="_scac" value="<?php echo $scac;?>">
												<!--end::Input-->
											</div>
											<!--end::Input group-->
											<!--begin::Input group-->
											<div class="d-flex flex-column mb-5 fv-row">
												<!--begin::Label-->
												<label class="fs-5 fw-semibold mb-2" for="_tdId">Transport Document ID</label>
												<!--end::Label-->
												<!--begin::Input-->
												<input required class="form-control form-control-solid" name="_tdId" id="_tdId" value="<?php echo $tdId;?>">
												<!--end::Input-->
											</div>
											<!--end::Input group-->
											<!--begin::Input group-->
											<div class="d-flex flex-column mb-5 fv-row">
												<!--begin::Label-->
												<label class="fs-5 fw-semibold mb-2" for="_tdType">Transport Document ID</label>
												<!--end::Label-->
												<!--begin::Input-->
												<select class="form-control form-control-solid" required id="_tdType" name="_tdType">
													<option value="BL" <?php if ( 'BL' === $tdType ) { echo 'selected="selected"'; } ?>>Bill of Lading ( BL )</option>
													<option value="BK" <?php if ( 'BK' === $tdType ) { echo 'selected="selected"'; } ?>>Booking Reference ( BK )</option>
													<option value="CN" <?php if ( 'CN' === $tdType ) { echo 'selected="selected"'; } ?>>Container Number ( CN )</option>
												</select>
												<!--end::Input-->
											</div>
											<!--end::Input group-->
											<!--begin::Submit-->
											<input type="submit" class="btn btn-primary" value="Save & Get shipment status" name="shipment_data">
											<!--end::Submit-->
											<?php
											$_webViewUrl = get_post_meta( $order_id, '_webViewUrl', true );
											if ( $_webViewUrl && '' != $_webViewUrl ) {
												?>
												<a target="_blank" href="<?php echo $_webViewUrl;?>" class="btn btn-success mt-3">Live tracking</a>
												<?php
											}
											?>
										</form>
										<?php
										if ( isset( $_POST['shipment_data'] ) ) {
											if (  $trackingResult ) {
												?>
												<div class="alert alert-success d-flex align-items-center p-5 mb-10">
													<!--begin::Svg Icon | path: icons/duotune/general/gen048.svg-->
													<span class="svg-icon svg-icon-2hx svg-icon-success me-4">
														<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"></path>
															<path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"></path>
														</svg>
													</span>
													<!--end::Svg Icon-->
													<div class="d-flex flex-column">
														<h4 class="mb-1 text-success">The data has been updated successfully</h4>
													</div>
												</div>
												<?php
											} else {
												?>
												<div class="alert alert-danger d-flex align-items-center p-5 mb-10">
													<!--begin::Svg Icon | path: icons/duotune/general/gen048.svg-->
													<span class="svg-icon svg-icon-2hx svg-icon-danger me-4">
														<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"></path>
															<path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"></path>
														</svg>
													</span>
													<!--end::Svg Icon-->
													<div class="d-flex flex-column">
														<h4 class="mb-1 text-danger">We couldn't get tracking info</h4>
													</div>
												</div>
												<?php
											}
										}
										?>
									</div>
									<!--end: Card Body-->
								</div>
							</div>
						</div>
					</div>
					<!--end::Tab pane-->
					<?php
					if ( Settings::get_setting( 'customers_chat_feature' ) ) {
						?>
						<!--begin::Tab pane-->
						<div class="tab-pane fade" id="order_chat">
							<div class="row gy-5 g-xl-10">
								<div class="col-xl-8 mb-5 mb-xl-10">
									<div class="card card-flush">
										<!--begin::Body-->
										<div class="card-body">
											<?php echo do_shortcode('[sacpro form_id="order'. $order_id .'"]')?>
										</div>
										<!--end: Card Body-->
									</div>
								</div>
							</div>
						</div>
						<!--end::Tab pane-->
						<?php
					}
					?>
				</div>
				<!--end::Tab content-->
			</div>
			<!--end::Order details page-->
		</div>
		<!--end::Container-->
	</div>
	<!--end::Post-->

	<!--begin::Modal - Upload File-->
	<div class="modal fade" id="kt_modal_upload" tabindex="-1" aria-hidden="true">
		<!--begin::Modal dialog-->
		<div class="modal-dialog modal-dialog-centered mw-650px">
			<!--begin::Modal content-->
			<div class="modal-content">
				<?php
				echo do_shortcode( '[wayz_uploader request_id="' . $order_id . '"]' );
				?>
			</div>
		</div>
	</div>
	<!--end::Modal - Upload File-->

	<div class="modal fade" id="request_file_modal" tabindex="-1" aria-hidden="true">
		<!--begin::Modal dialog-->
		<div class="modal-dialog modal-dialog-centered mw-650px">
			<!--begin::Modal content-->
			<div class="modal-content">
				<form action="?order_id=<?php echo $order_id; ?>" method="post" id="file_request_form" class="modal-body">
					<div class="form-group">
						<label for="comment" class="fw-bolder fs-4 mb-4">Comment</label>
						<textarea id="file_request_comment" class="form-control" name="file_request_comment" aria-required="true" required=""></textarea>
					</div>
					<div class="text-center pt-3">
						<button name="request_file_submit" id="request_file_submit" type="submit" class="btn btn-primary">
							Request
						</button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<div class="modal fade" tabindex="-1" id="reminder_modal">
		<div class="modal-dialog">
			<form class="modal-content fv-plugins-bootstrap5 fv-plugins-framework" method="post">
				<div class="modal-header">
					<h3 class="modal-title">Do you have any updates?</h3>

					<!--begin::Close-->
					<div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
						<i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
					</div>
					<!--end::Close-->
				</div>

				<div class="modal-body">
					<p>You can update the data or click remind me later to remind you after 24h.</p>

					<?php
					$fields          = Utils::get_system_report_header();
					$customer_fields = Utils::get_shipment_data_fields_by_customer();
					foreach ( $customer_fields as $fields_group ) {
						echo '<h1 class="fw-bold text-dark mb-9 mt-11">' . $fields_group['title'] . '</h1>';
						foreach ( $fields_group['fields'] as $key => $field ) {
							?>
							<!--begin::Input group-->
							<div class="d-flex flex-column mb-5 fv-row">
								<!--begin::Label-->
								<label class="fs-5 fw-semibold mb-2" for="<?php echo $key; ?>"><?php echo $fields[ $key ]; ?></label>
								<!--end::Label-->
								<!--begin::Input-->
								<input class="form-control form-control-solid" type="<?php echo $field['type']; ?>" name="<?php echo $key; ?>" id="<?php echo $key; ?>" value="<?php echo get_post_meta( $quotation_id, '_' . $key, true ); ?>">
								<!--end::Input-->
							</div>
							<!--end::Input group-->
							<?php
						}
					}
					?>
				</div>

				<div class="modal-footer">
					<input type="submit" class="btn btn-light" name="remind_later" value="Remind me tomorrow">
					<input type="submit" class="btn btn-primary" name="update_data" value="Save changes">
				</div>
			</form>
		</div>
	</div>

<?php
if ( ! get_transient( '_shipment_reminder_' . $order_id ) ) {
	?>
	<script type="text/javascript">
		jQuery(window).on('load', function() {
			jQuery('#reminder_modal').modal('show');
		});
	</script>
	<?php
}
get_footer();