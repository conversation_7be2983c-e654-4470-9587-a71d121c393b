# QA Test Plan: Authentication and Redirect Loop Fixes

## Overview
This test plan covers the fixes implemented for redirect loop issues and user_hash authentication problems in the Ways Backend freight management system.

## Issues Fixed
1. **Redirect Loop Issue**: Non-logged-in users accessing the website were stuck in an infinite redirect loop between homepage and login page
2. **User Hash Authentication Issue**: Shipping companies with correct user_hash parameters couldn't access the system properly
3. **Missing Exit Statements**: Multiple wp_redirect calls throughout the codebase were missing exit() statements

## Test Environment Setup
- **WordPress Version**: Latest compatible version
- **PHP Version**: 7.4+ or 8.0+
- **Browser**: Chrome, Firefox, Safari, Edge
- **Test Users**: 
  - Non-logged-in user (guest)
  - Customer user
  - Shipping company user
  - Administrator user

## Test Cases

### 1. Authentication and Redirect Flow Tests

#### TC-001: Non-logged-in User Access
**Objective**: Verify that non-logged-in users are properly redirected to login page without loops

**Pre-conditions**: 
- User is not logged in
- Clear browser cache and cookies

**Test Steps**:
1. Navigate to the website homepage
2. Try to access any protected page (e.g., dashboard, quotations, orders)
3. Verify redirect behavior

**Expected Results**:
- User should be redirected to login page (`/login/`)
- No redirect loops should occur
- Login page should load completely
- URL should remain on login page

**Priority**: High

---

#### TC-002: Login Page Access for Non-logged-in Users
**Objective**: Verify that non-logged-in users can access login page directly

**Test Steps**:
1. Navigate directly to login page URL
2. Verify page loads correctly
3. Check for any unexpected redirects

**Expected Results**:
- Login page loads successfully
- No redirects occur
- All login form elements are visible and functional

**Priority**: High

---

#### TC-003: Public Pages Access
**Objective**: Verify that public pages are accessible without login

**Test Steps**:
1. Access login page (`templates/page-login.php`)
2. Access registration page (`templates/page-registration.php`)
3. Access forgot password page (`templates/page-forgot-password.php`)

**Expected Results**:
- All public pages load without requiring authentication
- No redirects to login page occur

**Priority**: Medium

---

### 2. User Hash Authentication Tests

#### TC-004: Valid User Hash Authentication
**Objective**: Verify that shipping companies can authenticate using valid user_hash

**Pre-conditions**:
- Valid user_hash and hash_id parameters are available
- User exists in the system
- Hash exists in user's _hash_list meta

**Test Steps**:
1. Generate a valid user_hash for a shipping company user
2. Access any URL with `?user_hash=VALID_HASH&hash_id=USER_ID`
3. Verify authentication and redirect behavior

**Expected Results**:
- User should be automatically logged in
- Hash should be removed from user's _hash_list meta
- User should be redirected to clean URL (without hash parameters)
- User should have access to appropriate pages based on their role

**Priority**: High

---

#### TC-005: Invalid User Hash Authentication
**Objective**: Verify that invalid user_hash parameters are handled correctly

**Test Steps**:
1. Access URL with invalid user_hash: `?user_hash=INVALID_HASH&hash_id=USER_ID`
2. Access URL with invalid hash_id: `?user_hash=VALID_HASH&hash_id=999999`
3. Access URL with missing parameters

**Expected Results**:
- User should not be authenticated
- User should be redirected to login page if accessing protected content
- No errors should occur

**Priority**: Medium

---

#### TC-006: Hash Reusability
**Objective**: Verify that hashes can be reused multiple times

**Test Steps**:
1. Use a valid user_hash to authenticate
2. Logout
3. Try to use the same user_hash again
4. Repeat the process multiple times

**Expected Results**:
- Hash should work for authentication every time
- Hash should remain in user's _hash_list meta after use
- User should be able to authenticate multiple times with the same hash

**Priority**: High

---

### 3. Role-Based Access Control Tests

#### TC-007: Customer Role Access
**Objective**: Verify customer users can access appropriate pages

**Test Steps**:
1. Login as customer user
2. Access customer-allowed pages (dashboard, quotations, orders, etc.)
3. Try to access admin-only pages

**Expected Results**:
- Customer can access allowed pages
- Customer is redirected to no-permissions page for restricted content
- No redirect loops occur

**Priority**: Medium

---

#### TC-008: Shipping Company Role Access
**Objective**: Verify shipping company users can access appropriate pages

**Test Steps**:
1. Login as shipping company user
2. Access shipping company allowed pages
3. Try to access customer-only or admin-only pages

**Expected Results**:
- Shipping company can access allowed pages
- Proper redirects for unauthorized access
- No redirect loops occur

**Priority**: Medium

---

#### TC-009: Administrator Access
**Objective**: Verify administrators have full access

**Test Steps**:
1. Login as administrator
2. Access all system pages and functions

**Expected Results**:
- Administrator can access all pages
- No unauthorized access redirects
- All functionality works correctly

**Priority**: Low

---

### 4. Redirect Behavior Tests

#### TC-010: Login Redirect After Authentication
**Objective**: Verify proper redirect after successful login

**Test Steps**:
1. Access a protected page while not logged in
2. Get redirected to login page
3. Login with valid credentials
4. Verify redirect behavior

**Expected Results**:
- User should be redirected to homepage after login
- No redirect loops should occur

**Priority**: Medium

---

#### TC-011: Logout Redirect
**Objective**: Verify proper redirect after logout

**Test Steps**:
1. Login as any user
2. Logout
3. Verify redirect behavior

**Expected Results**:
- User should be redirected to homepage
- User should not have access to protected content
- Login page should be accessible

**Priority**: Medium

---

### 5. Edge Cases and Error Handling

#### TC-012: Concurrent Session Handling
**Objective**: Test behavior with multiple browser sessions

**Test Steps**:
1. Login in one browser/tab
2. Use user_hash authentication in another browser/tab
3. Verify session handling

**Expected Results**:
- Both sessions should work correctly
- No conflicts or errors should occur

**Priority**: Low

---

#### TC-013: Network Interruption During Authentication
**Objective**: Test behavior during network issues

**Test Steps**:
1. Start authentication process
2. Simulate network interruption
3. Resume network connection
4. Verify system behavior

**Expected Results**:
- System should handle interruptions gracefully
- User should be able to retry authentication
- No corrupt states should occur

**Priority**: Low

---

## Test Data Requirements

### User Accounts Needed:
- **Customer User**: 
  - Email: <EMAIL>
  - Role: customer
  - Organization: Test Organization

- **Shipping Company User**:
  - Email: <EMAIL>
  - Role: shipping_company
  - Valid hash in _hash_list meta

- **Administrator User**:
  - Email: <EMAIL>
  - Role: administrator

### Test URLs:
- Homepage: `/`
- Login Page: `/login/`
- Dashboard: `/dashboard/`
- Quotations: `/quotations/`
- Orders: `/orders/`

## Success Criteria
- [ ] All redirect loops are eliminated
- [ ] User hash authentication works correctly
- [ ] Hashes remain reusable after authentication
- [ ] All wp_redirect calls have proper exit() statements
- [ ] Role-based access control functions correctly
- [ ] No PHP errors or warnings occur
- [ ] Performance is not negatively impacted

## Test Execution Notes
- Test on multiple browsers and devices
- Clear cache between tests
- Monitor server logs for errors
- Document any unexpected behavior
- Verify database integrity after tests

## Regression Testing
After implementing fixes, run the following regression tests:
- [ ] All existing functionality works as expected
- [ ] User registration process is unaffected
- [ ] Password reset functionality works
- [ ] Email notifications are sent correctly
- [ ] File upload/download features work
- [ ] Reporting features function properly

## Sign-off
- [ ] Development Team Lead
- [ ] QA Team Lead  
- [ ] Product Owner
- [ ] System Administrator
