<?php

namespace Wayz\Permissions;

use <PERSON>z\Controllers\User;
use Wayz\Permissions\Roles\Customer;
use Wayz\Permissions\Roles\Role;
use Wayz\Permissions\Roles\Service_Provider;
use Wayz\Permissions\Roles\SP_Contact;

class Permissions {

	/**
	 *
	 * @return void
	 */
	public function __construct() {
		new Admin();
		$this->init_hooks();
	}

	/**
	 *
	 * @return void
	 */
	public function init_hooks() {
		add_action( 'template_redirect', array( $this, 'redirectIfNotAllowed' ) );
		add_action( 'pre_get_posts', array( $this, 'preGetPosts' ) );

		//add_filter( 'update_user_metadata', array( $this, 'update_user_metadata_permissions' ), 10, 5 );

		if ( ! wp_next_scheduled( 'wayz_check_offers' ) ) {
			wp_schedule_event( time(), 'daily', 'wayz_check_offers' );
		}

		add_action( 'wayz_check_offers', array( __NAMESPACE__ . '\Quotations_Offers', 'check_offers_expiration' ), 1 );
	}

	/**
	 * Check if user is allowed to overview the system
	 *
	 * @param int $user_id
	 *
	 * @return bool
	 */
	public static function isAllowedToOverview( int $user_id = 0 ) {
		if ( 0 === $user_id ) {
			$user_id = get_current_user_id();
		}

		$user = get_user_by( 'id', $user_id );
		if ( ! $user ) {
			return false;
		}

		/*
		 * Check if current user is an admin
		 */
		if ( in_array( 'administrator', (array) $user->roles ) ) {
			return true;
		}

		return false;
	}

	/**
	 * Check query args
	 *
	 * @param \WP_Query $query
	 *
	 * @return void
	 * @throws \WP_Error
	 */
	public static function preGetPosts( \WP_Query $query ) {
		if ( $query->is_main_query() ) {
			return;
		}

		$isAllowedToOverview = self::isAllowedToOverview();

		if ( $isAllowedToOverview ) {
			if ( ! empty( $query->get( 'author__in' ) ) ) {
				/**
				 * Use meta query instead of author, this is used to get posts based on company/organization ID
				 */
				$customers_ids = $query->get( 'author__in' );
				if ( count( $customers_ids ) == 1 ) {
					$query->set( 'meta_key', 'client_organization' );
					$query->set( 'meta_value', User::get_user_organization_id( $customers_ids[0] ) );
					$query->set( 'meta_compare', '=' );
				} else {
					$meta_query = array(
						'relation' => 'OR'
					);
					foreach ( $customers_ids as $customers_id ) {
						$meta_query[] = array(
							'key'     => 'client_organization',
							'value'   => User::get_user_organization_id( $customers_id ),
							'compare' => '=',
						);
					}

					$query->set( 'author__in', $meta_query );
				}

				$query->set( 'author__in', [] );
			}
		} else {
			$userID = get_current_user_id();
			if ( 0 === $userID ) {
				new \WP_Error( '403', 'Trying to get resources for guest' );
			}

			if ( ! empty( $query->get( 'author__in' ) ) ) {
				$query->set( 'author__in', [] );
			}

			if ( ! empty( $query->get( 'author' ) ) || ! empty( $query->get( 'customer_id' ) ) ) {
				/**
				 * Use meta query instead of author, this is used to get posts based on company/organization ID
				 */
				$query->set( 'meta_key', 'client_organization' );
				$query->set( 'meta_value', User::get_user_organization_id( $userID ) );
				$query->set( 'meta_compare', '=' );

				// Unset author and customer IDs
				$query->set( 'author', '' );
				$query->set( 'customer_id', '' );
			}

		}

	}

	/**
	 * Redirect User to homepage if tried to play around.
	 *
	 * @return void
	 */
	public function redirectIfNotAllowed() {

		if ( $this->handle_login_redirect() ) {
			return;
		}

		if ( self::isAllowedToOverview() ) {
			return;
		}

		$user = wp_get_current_user();

		// Get Current custom user role.
		/**
		 * ToDO: Refactoring
		 */
		$user_role = self::get_user_role( $user );

		// WordPress Roles Permissions
		if ( ! self::check_custom_permissions( $user ) ) {
			wp_redirect( self::get_no_permissions_page_url() );
			exit();
		}

		// Get current page/post type.
		$type = get_post_type( get_the_ID() );
		if ( $type && $user_role->can_access_post_type( $type ) ) {
			return;
		}

		wp_redirect( self::get_no_permissions_page_url() );
		exit();
	}

	/**
	 * Check if user is logged in or not and handle the redirection.
	 *
	 * @return bool.
	 */
	public function handle_login_redirect() {
		if ( isset( $_GET['user_hash'] ) && isset( $_GET['hash_id'] ) ) {
			$this->loginUsingHash( wc_clean( $_GET['user_hash'] ), intval( $_GET['hash_id'] ) );
		}

		$is_logged_in = is_user_logged_in();

		if ( ! $is_logged_in && ( $this->isLoginPage() || $this->isPublicPage() ) ) {
			return true;
		}

		if ( ! $is_logged_in && ! $this->isPublicPage() ) {
			wp_redirect( get_page_url_by_template( 'templates/page-login.php' ) );
			exit(); // Add exit to prevent redirect loop
		}

		if ( $is_logged_in && $this->isLoginPage() ) {
			wp_redirect( get_bloginfo( 'url' ) );
			exit(); // Add exit to prevent redirect loop
		}

		return false;
	}

	/**
	 * Login as a service provider using hash authentication
	 * Note: Hash remains reusable after authentication
	 *
	 * @param $hash
	 * @param $user_id
	 *
	 * @return void
	 */
	private function loginUsingHash( $hash, $user_id ) {
		if ( get_current_user_id() == $user_id ) {
			return;
		}

		$_hash_list = get_user_meta( $user_id, '_hash_list', true );
		if ( is_array( $_hash_list ) && in_array( $hash, $_hash_list ) ) {
			wp_clear_auth_cookie();
			wp_set_current_user( $user_id );
			wp_set_auth_cookie( $user_id );

			// Redirect to the same URL without the hash parameters to clean the URL
			$current_url = remove_query_arg( array( 'user_hash', 'hash_id' ) );
			wp_redirect( $current_url );
			exit();
		}
	}

	/**
	 * Check if current page is a public page.
	 *
	 * @return bool
	 */
	public function isPublicPage() {

		$publicPages = array(
			'templates/page-login.php',
			'templates/page-registration.php',
			'templates/page-forgot-password.php'
		);

		foreach ( $publicPages as $pageTemplate ) {
			if ( is_page_template( $pageTemplate ) ) {
				return true;
			}
		}

		return false;
	}


	/**
	 * Check if current page is login page
	 *
	 * @return bool
	 */
	private function isLoginPage() {
		if ( is_page_template( 'templates/page-login.php' ) ) {
			return true;
		}

		return in_array(
			$GLOBALS['pagenow'],
			array( 'wp-login.php', 'wp-register.php' ),
			true
		);
	}

	/**
	 * Check if post belong to an organization
	 *
	 * @param $post_id
	 * @param $user_id
	 *
	 * @return bool
	 */
	public static function check_post_organization( $post_id, $user_id ) {
		$client_organization = User::get_user_organization_id( $user_id );
		$post_organization   = get_post_meta( $post_id, 'client_organization', true );

		return $client_organization == $post_organization;
	}

	/**
	 * Check if data owner belong to the current user organization
	 *
	 * @param $owner_id
	 *
	 * @return bool
	 */
	public static function check_users_organization( $owner_id ) {
		$current_user_organization = User::get_current_user_organization_id();
		$data_owner_organization   = User::get_user_organization_id( $owner_id );

		return $current_user_organization == $data_owner_organization;
	}

	/**
	 * Check if the user is a Service Provider.
	 *
	 * @param  int  $user_id.
	 *
	 * @return bool.
	 */
	public static function is_service_provider( int $user_id ) {
		$user = get_user_by( 'id', $user_id );
		if ( in_array( 'shipping_company', (array) $user->roles ) ) {
			return true;
		}

		return false;
	}

	/**
	 * Get User Role.
	 *
	 * @param $user.
	 *
	 * @return Role.
	 */
	public static function get_user_role( $user ) {
		if ( in_array( 'shipping_company', (array) $user->roles ) ) {
			return new Service_Provider( $user->ID );
		}

		if ( in_array( 'sp_contact', (array) $user->roles ) ) {
			return new SP_Contact( $user->ID );
		}

		return new Customer( $user->ID );
	}

	/**
	 * Check if WordPress role can browse current page template.
	 *
	 * @param $user.
	 *
	 * @return bool.
	 */
	public static function check_custom_permissions( $user ) {
		$options            = get_option( 'custom_permissions_settings' );
		$allowed_templates  = $options['page_templates'][ $user->roles[0] ] ?? array();
		$allowed_post_types = $options['post_types'][ $user->roles[0] ] ?? array();

		$type         = get_post_type( get_the_ID() );
		$allowed_type = $type && in_array( $type, $allowed_post_types );

		if ( 'page' !== $type && $allowed_type ) {
			return true;
		}

		$page_template = get_page_template_slug( get_queried_object_id() );
		if ( $allowed_type && in_array( $page_template, $allowed_templates ) ) {
			return true;
		}

		return false;
	}

	/**
	 * Get "No Permissions" page url.
	 *
	 * @return string.
	 */
	public static function get_no_permissions_page_url() {
		$link = get_page_url_by_template( 'templates/page-no-permissions.php' );

		if ( is_null( $link ) ) {
			return get_bloginfo( 'url' );
		}

		return $link;
	}

}